{"env": {"browser": true, "commonjs": true, "es2021": true, "node": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "globals": {"process": "readonly", "Buffer": "readonly"}, "rules": {"no-unused-vars": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-undef": "error", "semi": ["error", "always"], "quotes": ["warn", "double"], "no-multiple-empty-lines": ["warn", {"max": 2}], "no-var": "error", "prefer-const": "warn", "eqeqeq": ["error", "always"], "no-trailing-spaces": "warn", "comma-dangle": ["warn", "always-multiline"], "indent": ["warn", 2]}, "ignorePatterns": ["**/*.test.js"]}