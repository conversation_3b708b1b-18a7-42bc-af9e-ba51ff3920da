trigger:
- master

variables:
  azureSubscription: "Lou_Malnati's-SC"
  appName: 'lou-web-app-prod'
  vmImageName: 'ubuntu-latest'

pool:
  vmImage: $(vmImageName)

jobs:
- job: Build
  displayName: 'Build and Package'
  steps:
  - bash: |
      npm install 
      npm test || exit 1
      npm run build --if-present
      npm prune --production
    displayName: 'Install, Test, and Build'

  - task: ArchiveFiles@2
    displayName: "Archive files"
    inputs:
      rootFolderOrFile: "$(System.DefaultWorkingDirectory)"
      includeRootFolder: false
      archiveFile: "$(Build.ArtifactStagingDirectory)/build$(Build.BuildId).zip"
      replaceExistingArchive: true

  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/build$(Build.BuildId).zip'
      artifactName: 'drop'

- deployment: Deploy
  displayName: 'Deploy to Production'
  environment: 'lou-web-app-prod'
  dependsOn: 'Build'
  strategy:
    runOnce:
      deploy:
        steps:
        - task: DownloadBuildArtifacts@0
          inputs:
            buildType: 'current'
            downloadType: 'single'
            artifactName: 'drop'
            downloadPath: '$(System.ArtifactsDirectory)'

        - task: AzureFunctionApp@2
          inputs:
            azureSubscription: '$(azureSubscription)'
            appType: 'functionApp'
            appName: '$(appName)'
            package: '$(System.ArtifactsDirectory)/drop/build$(Build.BuildId).zip'
            deploymentMethod: 'auto'