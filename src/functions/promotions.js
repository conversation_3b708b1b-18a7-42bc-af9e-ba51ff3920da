const { app } = require("@azure/functions");
const { executeQuery } = require("./helper/helper");
const { authenticate } = require("./helper/auth");
const { withLogging } = require("./helper/loggingMiddleware");
// const moment = require("moment-timezone"); // Not currently used
const { ROLES } = require("./helper/constants");
const CONFIG = require("./config");

// get promotions from SQL DB
// * LOUS_EDW_DEV.dbo.Daily_Comp_Promotions
app.http("getPromotions", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "get-promotions",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P]
        `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Get specific promotion by fiscal data
app.http("getPromotionByFiscalData", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "get-promotion-by-fiscal",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const fiscal_year = request.query.get("fiscal_year");
      const fiscal_period = request.query.get("fiscal_period");
      const fiscal_week = request.query.get("fiscal_week");
      context.log(fiscal_year, fiscal_period, fiscal_week);

      if (!fiscal_year || !fiscal_period || !fiscal_week) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year, fiscal_period, and fiscal_week query parameters are required" },
        };
      }

      if (isNaN(fiscal_year)) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year should be an integer" },
        };
      }

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P]
        WHERE fiscal_year = @fiscal_year 
          AND fiscal_period = @fiscal_period 
          AND fiscal_week = @fiscal_week
        `;

      const result = await executeQuery(sqlQuery, {
        fiscal_year: parseInt(fiscal_year),
        fiscal_period,
        fiscal_week,
      });

      if (result.length === 0) {
        return {
          status: 404,
          jsonBody: {
            message: "No promotion found for the specified fiscal year, period, and week",
            query: sqlQuery,
          },
        };
      }

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result: result[0],
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Add promotion
app.http("addPromotion", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "add-promotion",
  handler: withLogging(async (request, context, setLoggingData) => {
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER]);

      const body = await request.json();
      const { fiscal_year, fiscal_period, fiscal_week, holidayAndEvents, marketingPromotion, loyaltyCampaigns } = body;

      if (!fiscal_year || !fiscal_period || !fiscal_week) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year, fiscal_period, and fiscal_week are mandatory" },
        };
      }

      if (isNaN(fiscal_year)) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year should be an integer" },
        };
      }

      const currentYearPWeekKey = `${fiscal_year}-${fiscal_period}-${fiscal_week}`;

      const existingPromotion = await executeQuery(
        `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] WHERE [current_year p-week Key] = @current_year_p_week_key`,
        {
          current_year_p_week_key: currentYearPWeekKey,
        },
      );

      if (existingPromotion.length > 0) {
        return {
          status: 400,
          jsonBody: { error: "Promotion with this fiscal year/period/week combination already exists" },
        };
      }

      const sqlQuery = `
        INSERT INTO ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] 
        (fiscal_year, fiscal_period, fiscal_week, [current_year p-week Key], [Holiday and Events], [Marketing Pramotion], [Loyalty Campaigns])
        VALUES 
        (@fiscal_year, @fiscal_period, @fiscal_week, @current_year_p_week_key, @holiday_and_events, @marketing_promotion, @loyalty_campaigns);
      `;

      const queryParams = {
        fiscal_year,
        fiscal_period,
        fiscal_week,
        current_year_p_week_key: currentYearPWeekKey,
        holiday_and_events: holidayAndEvents || "",
        marketing_promotion: marketingPromotion || "",
        loyalty_campaigns: loyaltyCampaigns || "",
      };

      await executeQuery(sqlQuery, queryParams);

      setLoggingData(tokenResult, sqlQuery);

      return {
        status: 200,
        jsonBody: {
          message: "Promotion added successfully",
          promotion: {
            fiscal_year,
            fiscal_period,
            fiscal_week,
            "current_year p-week Key": currentYearPWeekKey,
            "Holiday and Events": holidayAndEvents || "",
            "Marketing Pramotion": marketingPromotion || "",
            "Loyalty Campaigns": loyaltyCampaigns || "",
          },
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  }),
});

// Update promotion
app.http("updatePromotion", {
  methods: ["PUT"],
  authLevel: "anonymous",
  route: "update-promotion",
  handler: withLogging(async (request, context, setLoggingData) => {
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER]);

      const body = await request.json();
      const { originalKey, fiscal_year, fiscal_period, fiscal_week, holidayAndEvents, marketingPromotion, loyaltyCampaigns, lastYearHolidayAndEvents, lastYearMarketingPromotion, lastYearLoyaltyCampaigns } = body;

      if (!originalKey) {
        return {
          status: 400,
          jsonBody: { error: "originalKey is required to identify the record to update" },
        };
      }

      if (!fiscal_year || !fiscal_period || !fiscal_week) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year, fiscal_period, and fiscal_week are mandatory" },
        };
      }

      if (isNaN(fiscal_year)) {
        return {
          status: 400,
          jsonBody: { error: "fiscal_year should be an integer" },
        };
      }

      const existingPromotion = await executeQuery(
        `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] WHERE [current_year p-week Key] = @original_key`,
        {
          original_key: originalKey,
        },
      );

      if (existingPromotion.length === 0) {
        return {
          status: 404,
          jsonBody: { error: "Promotion not found" },
        };
      }

      const newCurrentYearPWeekKey = `${fiscal_year}-${fiscal_period}-${fiscal_week}`;

      if (newCurrentYearPWeekKey !== originalKey) {
        const duplicateCheck = await executeQuery(
          `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] WHERE [current_year p-week Key] = @new_key`,
          {
            new_key: newCurrentYearPWeekKey,
          },
        );

        if (duplicateCheck.length > 0) {
          return {
            status: 400,
            jsonBody: { error: "A promotion with the new fiscal year/period/week combination already exists" },
          };
        }
      }

      const sqlQuery = `
        UPDATE ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] 
        SET fiscal_year = @fiscal_year,
            fiscal_period = @fiscal_period,
            fiscal_week = @fiscal_week,
            [current_year p-week Key] = @current_year_p_week_key,
            [Holiday and Events] = @holiday_and_events,
            [Marketing Pramotion] = @marketing_promotion,
            [Loyalty Campaigns] = @loyalty_campaigns
        WHERE [current_year p-week Key] = @original_key;
      `;

      const queryParams = {
        fiscal_year,
        fiscal_period,
        fiscal_week,
        current_year_p_week_key: newCurrentYearPWeekKey,
        holiday_and_events: holidayAndEvents || "",
        marketing_promotion: marketingPromotion || "",
        loyalty_campaigns: loyaltyCampaigns || "",
        original_key: originalKey,
      };

      await executeQuery(sqlQuery, queryParams);

      // Update last year's data if provided
      if (lastYearHolidayAndEvents !== undefined || lastYearMarketingPromotion !== undefined || lastYearLoyaltyCampaigns !== undefined) {
        const lastYearKey = `${fiscal_year - 1}-${fiscal_period}-${fiscal_week}`;

        // Check if last year record exists
        const lastYearPromotion = await executeQuery(
          `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] WHERE [current_year p-week Key] = @last_year_key`,
          {
            last_year_key: lastYearKey,
          },
        );

        if (lastYearPromotion.length > 0) {
          // Update existing last year record
          const updateLastYearQuery = `
            UPDATE ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] 
            SET [Holiday and Events] = @holiday_and_events,
                [Marketing Pramotion] = @marketing_promotion,
                [Loyalty Campaigns] = @loyalty_campaigns
            WHERE [current_year p-week Key] = @last_year_key;
          `;

          await executeQuery(updateLastYearQuery, {
            holiday_and_events: lastYearHolidayAndEvents || lastYearPromotion[0]["Holiday and Events"] || "",
            marketing_promotion: lastYearMarketingPromotion || lastYearPromotion[0]["Marketing Pramotion"] || "",
            loyalty_campaigns: lastYearLoyaltyCampaigns || lastYearPromotion[0]["Loyalty Campaigns"] || "",
            last_year_key: lastYearKey,
          });
        } else {
          // Create new last year record if it doesn't exist
          const insertLastYearQuery = `
            INSERT INTO ${CONFIG.dbName}.${CONFIG.schema}.[Daily_Sales_D&P] 
            (fiscal_year, fiscal_period, fiscal_week, [current_year p-week Key], [Holiday and Events], [Marketing Pramotion], [Loyalty Campaigns])
            VALUES 
            (@fiscal_year, @fiscal_period, @fiscal_week, @current_year_p_week_key, @holiday_and_events, @marketing_promotion, @loyalty_campaigns);
          `;

          await executeQuery(insertLastYearQuery, {
            fiscal_year: fiscal_year - 1,
            fiscal_period,
            fiscal_week,
            current_year_p_week_key: lastYearKey,
            holiday_and_events: lastYearHolidayAndEvents || "",
            marketing_promotion: lastYearMarketingPromotion || "",
            loyalty_campaigns: lastYearLoyaltyCampaigns || "",
          });
        }
      }

      setLoggingData(tokenResult, sqlQuery);

      return {
        status: 200,
        jsonBody: {
          message: "Promotion updated successfully",
          promotion: {
            fiscal_year,
            fiscal_period,
            fiscal_week,
            "current_year p-week Key": newCurrentYearPWeekKey,
            "Holiday and Events": holidayAndEvents || "",
            "Marketing Pramotion": marketingPromotion || "",
            "Loyalty Campaigns": loyaltyCampaigns || "",
          },
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  }),
});
