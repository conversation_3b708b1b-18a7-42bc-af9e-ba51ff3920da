const DB_SERVER = process.env["DB_SERVER"];
const DB_USER = process.env["DB_USER"];
const DB_PASSWORD = process.env["DB_PASSWORD"];
const DB_NAME = process.env["DB_NAME"];
const SCHEMA = process.env["SCHEMA"];
const STORE_LIST_TABLE = process.env["STORE_LIST_TABLE"];
const STORE_LOGISTICS_MANAGER_TABLE = process.env["STORE_LOGISTICS_MANAGER_TABLE"];
const FISCAL_CALENDAR_TABLE = process.env["FISCAL_CALENDAR_TABLE"];
const FISCAL_CALENDAR_VIEW = process.env["FISCAL_CALENDAR_VIEW"];
const AZURE_TENANT_ID = process.env["AZURE_TENANT_ID"];
const AZURE_CLIENT_ID = process.env["AZURE_CLIENT_ID"];
const AZURE_CLIENT_SECRET = process.env["AZURE_CLIENT_SECRET"];
const AZURE_SYNAPSE_ENDPOINT = process.env["AZURE_SYNAPSE_ENDPOINT"];
const VW_LOU_DATA_VALIDATION = process.env["VW_LOU_DATA_VALIDATION"];
const DB_NAME_EMS = process.env["DB_NAME_EMS"];
const DB_USER_EMS = process.env["DB_USER_EMS"];
const DB_PASSWORD_EMS = process.env["DB_PASSWORD_EMS"];

module.exports = {
  dbConfigEms: {
    server: DB_SERVER,
    authentication: {
      type: "default",
      options: {
        userName: DB_USER_EMS,
        password: DB_PASSWORD_EMS,
      },
    },
    options: {
      database: DB_NAME_EMS,
      encrypt: true,
      rowCollectionOnRequestCompletion: true,
      requestTimeout: 1200000,
      connectionTimeout: 1200000,
    },
  },
  dbConfig: {
    server: DB_SERVER,
    authentication: {
      type: "default",
      options: {
        userName: DB_USER,
        password: DB_PASSWORD,
      },
    },
    options: {
      database: DB_NAME,
      encrypt: true,
      rowCollectionOnRequestCompletion: true,
      requestTimeout: 1200000,
      connectionTimeout: 1200000,
    },
  },
  dbName: DB_NAME,
  dbNameEms: DB_NAME_EMS,
  schema: SCHEMA,
  storeListTableName: STORE_LIST_TABLE,
  storeLogisticsManagerTableName: STORE_LOGISTICS_MANAGER_TABLE,
  fiscalCalendarTableName: FISCAL_CALENDAR_TABLE,
  fiscalCalendarViewName: FISCAL_CALENDAR_VIEW,
  AZURE_TENANT_ID: AZURE_TENANT_ID,
  AZURE_CLIENT_ID: AZURE_CLIENT_ID,
  AZURE_CLIENT_SECRET: AZURE_CLIENT_SECRET,
  AZURE_SYNAPSE_ENDPOINT: AZURE_SYNAPSE_ENDPOINT,
  VW_LOU_DATA_VALIDATION: VW_LOU_DATA_VALIDATION,
};
