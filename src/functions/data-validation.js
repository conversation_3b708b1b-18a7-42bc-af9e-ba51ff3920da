const { app } = require("@azure/functions");
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");
const CONFIG = require("./config");
const { executeQuery } = require("./helper/helper");
const { ROLES } = require("./helper/constants");
const moment = require("moment-timezone");
const { authenticate } = require("./helper/auth");

const credential = new DefaultAzureCredential();
const vaultName = process.env["KEY_VAULT_NAME"];
const url = `https://${vaultName}.vault.azure.net`;
const client = new SecretClient(url, credential);

app.http("getDataValidation", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-data-validation",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { page = 1, limit = 200, filters = {} } = body ? JSON.parse(body) : {};

      const offset = (page - 1) * limit;

      let whereClause = "";
      const filterConditions = [];
      const params = {};

      if (filters.store) {
        filterConditions.push("store_name LIKE @storeName");
        params.storeName = `%${filters.store}%`;
      }

      if (filters.businessDate) {
        const date = moment(filters.businessDate).format("YYYY-MM-DD");
        filterConditions.push("business_date = @businessDate");
        params.businessDate = date;
      }

      if (filterConditions.length > 0) {
        whereClause = `WHERE ${filterConditions.join(" AND ")}`;
      }

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) AS count 
        FROM ${CONFIG.dbName}.${CONFIG.schema}.${CONFIG.VW_LOU_DATA_VALIDATION}
        ${whereClause}
      `;

      const countResult = await executeQuery(countQuery, params);
      const totalCount = countResult[0].count;
      const totalPages = Math.ceil(totalCount / limit);

      // Main query with pagination
      sqlQuery = `
        SELECT * 
        FROM ${CONFIG.dbName}.${CONFIG.schema}.${CONFIG.VW_LOU_DATA_VALIDATION}
        ${whereClause}
        ORDER BY business_date DESC
        OFFSET ${offset} ROWS 
        FETCH NEXT ${limit} ROWS ONLY
      `;

      context.log("sqlQuery", sqlQuery);
      context.log("params", params);

      const result = await executeQuery(sqlQuery, params);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName: `${CONFIG.dbName}.${CONFIG.schema}.${CONFIG.VW_LOU_DATA_VALIDATION}`,
          limit,
          currentPage: page,
          totalPages,
          totalCount,
          count: result.length,
          filters: {
            store: filters.store,
            businessDate: filters.businessDate,
          },
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  },
});

app.http("getConfigForDataValidation", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "data-validation/config",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const query = `
        SELECT DISTINCT store_name
        FROM ${CONFIG.dbName}.${CONFIG.schema}.${CONFIG.VW_LOU_DATA_VALIDATION}
        WHERE store_name IS NOT NULL AND store_name != ''
        ORDER BY store_name ASC
      `;

      context.log("Query:", query);
      const result = await executeQuery(query);

      const config = {
        stores: result.map((row) => row.store_name),
      };

      return {
        status: 200,
        jsonBody: { config },
      };
    } catch (error) {
      context.log("Error in getConfigForDataValidation:", error);
      return {
        status: 500,
        jsonBody: { error: "An error occurred while fetching the configuration." },
      };
    }
  },
});
