const { app } = require("@azure/functions");
const { DefaultAzureCredential, ClientSecretCredential } = require("@azure/identity");
const { ArtifactsClient } = require("@azure/synapse-artifacts");
const CONFIG = require("./config");
const { authenticate } = require("./helper/auth");
const moment = require("moment-timezone");
const { ROLES, allowedFields } = require("./helper/constants");

const credential = new ClientSecretCredential(CONFIG.AZURE_TENANT_ID, CONFIG.AZURE_CLIENT_ID, CONFIG.AZURE_CLIENT_SECRET);

const client = new ArtifactsClient(credential, CONFIG.AZURE_SYNAPSE_ENDPOINT);

async function getAllPipelineRuns(queryParams) {
  let allRuns = [];
  let continuationToken = null;

  do {
    const queryParamsWithToken = continuationToken ? { ...queryParams, continuationToken } : queryParams;

    const response = await client.pipelineRunOperations.queryPipelineRunsByWorkspace(queryParamsWithToken);

    if (response.value && response.value.length > 0) {
      allRuns = allRuns.concat(response.value);
    }

    continuationToken = response.continuationToken;
  } while (continuationToken);

  return allRuns;
}

async function getPipelineRunStatus(runId) {
  try {
    const runResponse = await client.pipelineRunOperations.getPipelineRun(runId);
    return runResponse;
  } catch (error) {
    console.error(`Error getting pipeline run status: ${error.message}`);
    throw error;
  }
}

async function getActivityRunsForPipeline(pipelineName, runId) {
  try {
    const filterParameters = {
      lastUpdatedAfter: moment().tz("America/Chicago").subtract(1, "days").toDate(),
      lastUpdatedBefore: moment().tz("America/Chicago").toDate(),
      filters: [],
    };

    const activityRuns = await client.pipelineRunOperations.queryActivityRuns(pipelineName, runId, filterParameters);

    return activityRuns.value;
  } catch (error) {
    console.error(`Error getting activity runs: ${error.message}`);
    throw error;
  }
}

app.http("getPipelineStatus", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "pipeline-status",
  handler: async (request, context) => {
    try {
      const { runId } = await request.json();

      if (!runId) {
        return {
          status: 400,
          body: JSON.stringify({ error: "Missing required parameter", message: "runId is required" }),
        };
      }

      const runDetails = await getPipelineRunStatus(runId);

      let estimatedTimeRemaining = null;
      if (runDetails.status === "InProgress" && runDetails.runStart) {
        estimatedTimeRemaining = "Calculating...";
      }

      let activityStats = null;
      let progressPercentage = null;

      if (runDetails.pipelineName) {
        try {
          const activityRuns = await getActivityRunsForPipeline(runDetails.pipelineName, runId);

          const statusCounts = activityRuns.reduce((counts, activity) => {
            counts[activity.status] = (counts[activity.status] || 0) + 1;
            return counts;
          }, {});

          const totalActivities = activityRuns.length;
          const completedActivities = activityRuns.filter(
            (run) => run.status === "Succeeded" || run.status === "Failed" || run.status === "Skipped" || run.status === "Inactive"
          ).length;

          progressPercentage = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

          const errors = activityRuns
            .filter((run) => run.status === "Failed" && run.error)
            .map((run) => ({
              activityName: run.activityName,
              errorMessage: run.error.message,
              errorCode: run.error.errorCode,
              details: run.error.details,
            }));

          activityStats = {
            totalActivities,
            statusCounts,
            completedActivities,
            errors: errors.length > 0 ? errors : null,
          };
        } catch (activityError) {
          context.log(`Warning: Failed to get activity runs: ${activityError.message}`);
        }
      }

      return {
        jsonBody: {
          runId,
          status: runDetails.status,
          pipelineName: runDetails.pipelineName,
          runStart: runDetails.runStart,
          runEnd: runDetails.runEnd,
          durationInMs: runDetails.durationInMs,
          progressPercentage,
          estimatedTimeRemaining,
          lastUpdated: runDetails.lastUpdated,
          parameters: runDetails.parameters,
          runDimension: runDetails.runDimension,
          isLatest: runDetails.isLatest,
          activityStats,
        },
      };
    } catch (error) {
      context.log(`Error in getPipelineStatus: ${error.message}`);
      return { status: 500, body: JSON.stringify({ error: "Something went wrong!", message: error.message }) };
    }
  },
});

app.http("getPipelineRuns", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "pipeline-runs",
  handler: async (request, context) => {
    try {
      const { pipelineName, startTime, endTime } = await request.json();

      let updatedAfter;
      let updatedBefore;

      if (startTime && endTime) {
        updatedAfter = moment(startTime).toDate();
        updatedBefore = moment(endTime).toDate();
      } else {
        updatedAfter = moment().tz("America/Chicago").subtract(1, "days").toDate();
        updatedBefore = moment().tz("America/Chicago").toDate();
      }

      const queryParams = {
        lastUpdatedAfter: updatedAfter,
        lastUpdatedBefore: updatedBefore,
        filters: [
          {
            operand: "PipelineName",
            operator: "Equals",
            values: [pipelineName],
          },
        ],
      };

      const runs = await getAllPipelineRuns(queryParams);

      return { jsonBody: { value: runs, continuationToken: null } };
    } catch (error) {
      context.log(`Error in getPipelineRuns: ${error.message}`);
      return { status: 500, body: JSON.stringify({ error: "Something went wrong!", message: error.message }) };
    }
  },
});

app.http("getPipelineActivityRuns", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "pipeline-activity-runs",
  handler: async (request, context) => {
    try {
      const { pipelineName, runId, filterOptions = {} } = await request.json();

      if (!pipelineName || !runId) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Missing required parameters",
            message: "Both pipelineName and runId are required",
          }),
        };
      }

      const defaultFilters = {
        lastUpdatedAfter: moment().tz("America/Chicago").subtract(1, "days").toDate(),
        lastUpdatedBefore: moment().tz("America/Chicago").toDate(),
        filters: [],
      };

      const filterParameters = {
        ...defaultFilters,
        ...filterOptions,
      };

      const activityRuns = await client.pipelineRunOperations.queryActivityRuns(pipelineName, runId, filterParameters);

      const formattedActivityRuns = activityRuns.value.map((run) => ({
        activityName: run.activityName,
        activityType: run.activityType,
        status: run.status,
        startTime: run.output?.executionStartTime || run.startTime,
        endTime: run.output?.executionEndTime || run.endTime,
        durationInMs: run.durationInMs,
        error: run.error,
        input: run.input,
        output: run.output,
        retryAttempt: run.retryAttempt,
      }));

      const totalActivities = formattedActivityRuns.length;
      const completedActivities = formattedActivityRuns.filter(
        (run) => run.status === "Succeeded" || run.status === "Failed" || run.status === "Skipped"
      ).length;

      const progressPercentage = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

      return {
        jsonBody: {
          pipelineName,
          runId,
          progressPercentage,
          totalActivities,
          completedActivities,
          continuationToken: activityRuns.continuationToken,
          activities: formattedActivityRuns,
        },
      };
    } catch (error) {
      context.log(`Error in getPipelineActivityRuns: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Failed to retrieve activity runs",
          message: error.message,
        }),
      };
    }
  },
});

app.http("triggerPipeline", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "trigger-pipeline",
  handler: async (request, context) => {
    try {
      const { pipelineName, parameters } = await request.json();
      const runResponse = await client.pipelineOperations.createPipelineRun(pipelineName, { parameters });
      return { jsonBody: { pipelineName, runId: runResponse.runId, message: "Pipeline run triggered successfully" } };
    } catch (error) {
      context.log(`Error in triggerPipeline: ${error.message}`);
      return { status: 500, body: JSON.stringify({ error: "Something went wrong!", message: error.message }) };
    }
  },
});

app.http("cancelPipelineRun", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "cancel-pipeline-run",
  handler: async (request, context) => {
    try {
      const { runId } = await request.json();

      if (!runId) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Missing required parameter",
            message: "runId is required",
          }),
        };
      }

      const runDetails = await getPipelineRunStatus(runId);

      if (runDetails.status !== "InProgress" && runDetails.status !== "Queued" && runDetails.status !== "Canceling") {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Invalid operation",
            message: `Pipeline run with status '${runDetails.status}' cannot be canceled.`,
          }),
        };
      }

      await client.pipelineRunOperations.cancelPipelineRun(runId);

      return {
        jsonBody: {
          runId,
          message: "Pipeline run cancellation has been initiated",
          pipelineName: runDetails.pipelineName,
        },
      };
    } catch (error) {
      context.log(`Error in cancelPipelineRun: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Failed to cancel pipeline run",
          message: error.message,
        }),
      };
    }
  },
});

app.http("getPipelineList", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-pipeline-list",
  handler: async (request, context) => {
    try {
      const { page = 1, limit = 10, search = "" } = await request.json();
      const skip = (page - 1) * limit;

      const pipelines = [];
      const pipelineIterator = client.pipelineOperations.listPipelinesByWorkspace();

      for await (const pipelinePage of pipelineIterator.byPage()) {
        for (const pipeline of pipelinePage) {
          if (search === "" || pipeline.name.toLowerCase().includes(search.toLowerCase())) {
            pipelines.push({
              name: pipeline.name ?? "",
              id: pipeline.id ?? "",
              type: pipeline.type ?? "",
              folder: pipeline.folder ?? "",
            });
          }
        }
      }

      const totalPipelines = pipelines.length;
      const totalPages = Math.ceil(totalPipelines / limit);
      const paginatedPipelines = pipelines.slice(skip, skip + limit);

      return {
        jsonBody: {
          count: paginatedPipelines.length,
          totalPipelines: totalPipelines,
          page: page,
          totalPages: totalPages,
          pipelines: paginatedPipelines,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
          search: search,
          endpoint: CONFIG.AZURE_SYNAPSE_ENDPOINT,
        },
      };
    } catch (error) {
      context.log(`Error in getPipelineList: ${error.message}`);
      return { status: 500, body: JSON.stringify({ error: "Something went wrong!", message: error.message }) };
    }
  },
});

app.http("getAllPipelinesStatus", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "all-pipeline-statuses",
  handler: async (request, context) => {
    try {
      const body = await request.text();
      const { startTime = moment().tz("America/Chicago").subtract(1, "days").toDate(), endTime = moment().tz("America/Chicago").toDate() } = body
        ? JSON.parse(body)
        : {};

      const queryParams = {
        lastUpdatedAfter: startTime,
        lastUpdatedBefore: endTime,
      };

      const allRuns = await getAllPipelineRuns(queryParams);

      let totalRuns = 0;
      let successfulRuns = 0;
      let failedRuns = 0;
      let totalDuration = 0;
      const pipelineStats = {};

      for (const run of allRuns) {
        const runDetails = await client.pipelineRunOperations.getPipelineRun(run.runId);
        totalRuns += 1;
        const isSuccessful = runDetails.status === "Succeeded";
        if (isSuccessful) {
          successfulRuns += 1;
        } else if (runDetails.status === "Failed") {
          failedRuns += 1;
        }
        totalDuration += runDetails.durationInMs;

        if (!pipelineStats[runDetails.pipelineName]) {
          pipelineStats[runDetails.pipelineName] = {
            totalRuns: 0,
            successfulRuns: 0,
            failedRuns: 0,
            totalDuration: 0,
          };
        }

        pipelineStats[runDetails.pipelineName].totalRuns += 1;
        if (isSuccessful) {
          pipelineStats[runDetails.pipelineName].successfulRuns += 1;
        } else {
          pipelineStats[runDetails.pipelineName].failedRuns += 1;
        }
        pipelineStats[runDetails.pipelineName].totalDuration += runDetails.durationInMs;
      }

      const successRate = (successfulRuns / totalRuns) * 100;
      const overallStats = {
        totalRuns,
        successfulRuns,
        failedRuns,
        successRate: `${successRate.toFixed(2)}%`,
        totalDuration: `${(totalDuration / 1000 / 60).toFixed(2)} minutes`,
        startTime,
        endTime,
      };

      const pipelineSpecificStats = Object.keys(pipelineStats).map((pipelineName) => ({
        pipelineName,
        ...pipelineStats[pipelineName],
        successRate: `${((pipelineStats[pipelineName].successfulRuns / pipelineStats[pipelineName].totalRuns) * 100).toFixed(2)}%`,
        totalDuration: `${(pipelineStats[pipelineName].totalDuration / 1000 / 60).toFixed(2)} minutes`,
      }));

      const pipelineTotalRuns = pipelineSpecificStats.reduce((total, pipeline) => total + pipeline.totalRuns, 0);

      if (pipelineTotalRuns !== totalRuns) {
        context.log(`Mismatch in total runs: Expected ${totalRuns}, but pipeline-specific total is ${pipelineTotalRuns}.`);
        return {
          status: 500,
          body: JSON.stringify({
            error: "Mismatch in pipeline and overall run counts",
            message: `Overall total runs: ${totalRuns}, Pipeline-specific total runs: ${pipelineTotalRuns}.`,
          }),
        };
      }

      return {
        jsonBody: {
          overallStats,
          pipelineSpecificStats,
        },
      };
    } catch (error) {
      context.log(`Error in getAllPipelinesStatus: ${error.message}`);
      return { status: 500, body: JSON.stringify({ error: "Something went wrong!", message: error.message }) };
    }
  },
});

app.http("getDailyPipelineMetrics", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "daily-pipeline-metrics",
  handler: async (request, context) => {
    try {
      const body = await request.text();
      const {
        startTime = moment().tz("America/Chicago").subtract(1, "days").toDate(),
        endTime = moment().tz("America/Chicago").toDate(),
        pipelineName = null,
      } = body ? JSON.parse(body) : {};

      const startDateTime = moment.tz(startTime, "YYYY-MM-DD", "America/Chicago").startOf("day").toDate();
      const endDateTime = moment.tz(endTime, "YYYY-MM-DD", "America/Chicago").endOf("day").toDate();

      const dateRange = [];
      const currentDate = moment(startDateTime);
      const lastDate = moment(endDateTime);

      while (currentDate.isSameOrBefore(lastDate, "day")) {
        dateRange.push(currentDate.format("YYYY-MM-DD"));
        currentDate.add(1, "day");
      }

      const queryParams = {
        lastUpdatedAfter: startDateTime,
        lastUpdatedBefore: endDateTime,
        filters: [],
      };

      if (pipelineName) {
        queryParams.filters.push({
          operand: "PipelineName",
          operator: "Equals",
          values: [pipelineName],
        });
      }

      const allRuns = await getAllPipelineRuns(queryParams);

      const dailyMetrics = {};
      dateRange.forEach((date) => {
        dailyMetrics[date] = {
          totalRuns: 0,
          successfulRuns: 0,
          failedRuns: 0,
          totalDuration: 0,
          avgDuration: 0,
          pipelines: {},
        };
      });

      for (const run of allRuns) {
        try {
          const runDetails = await client.pipelineRunOperations.getPipelineRun(run.runId);

          if (!runDetails.runStart) continue;

          const runDate = moment.tz(runDetails.runStart, "America/Chicago").format("YYYY-MM-DD");

          if (!dailyMetrics[runDate]) continue;

          dailyMetrics[runDate].totalRuns += 1;

          const isSuccessful = runDetails.status === "Succeeded";
          if (isSuccessful) {
            dailyMetrics[runDate].successfulRuns += 1;
          } else if (runDetails.status === "Failed") {
            dailyMetrics[runDate].failedRuns += 1;
          }

          if (runDetails.durationInMs) {
            dailyMetrics[runDate].totalDuration += runDetails.durationInMs;
          }

          if (!dailyMetrics[runDate].pipelines[runDetails.pipelineName]) {
            dailyMetrics[runDate].pipelines[runDetails.pipelineName] = {
              totalRuns: 0,
              successfulRuns: 0,
              failedRuns: 0,
              totalDuration: 0,
            };
          }

          const pipelineMetrics = dailyMetrics[runDate].pipelines[runDetails.pipelineName];
          pipelineMetrics.totalRuns += 1;

          if (isSuccessful) {
            pipelineMetrics.successfulRuns += 1;
          } else if (runDetails.status === "Failed") {
            pipelineMetrics.failedRuns += 1;
          }

          if (runDetails.durationInMs) {
            pipelineMetrics.totalDuration += runDetails.durationInMs;
          }
        } catch (runError) {
          context.log(`Error processing run ${run.runId}: ${runError.message}`);
        }
      }

      const formattedDailyMetrics = Object.keys(dailyMetrics).map((date) => {
        const metrics = dailyMetrics[date];

        metrics.avgDuration = metrics.totalRuns > 0 ? Math.round(metrics.totalDuration / metrics.totalRuns) : 0;

        const pipelineMetrics = Object.keys(metrics.pipelines).map((pipeline) => {
          const pData = metrics.pipelines[pipeline];
          const successRate = pData.totalRuns > 0 ? (pData.successfulRuns / pData.totalRuns) * 100 : 0;
          const avgDuration = pData.totalRuns > 0 ? Math.round(pData.totalDuration / pData.totalRuns) : 0;

          return {
            pipelineName: pipeline,
            totalRuns: pData.totalRuns,
            successfulRuns: pData.successfulRuns,
            failedRuns: pData.failedRuns,
            successRate: `${successRate.toFixed(2)}%`,
            avgDuration: `${(avgDuration / 1000 / 60).toFixed(2)} minutes`,
            totalDuration: `${(pData.totalDuration / 1000 / 60).toFixed(2)} minutes`,
          };
        });

        const successRate = metrics.totalRuns > 0 ? (metrics.successfulRuns / metrics.totalRuns) * 100 : 0;

        return {
          date,
          totalRuns: metrics.totalRuns,
          successfulRuns: metrics.successfulRuns,
          failedRuns: metrics.failedRuns,
          successRate: `${successRate.toFixed(2)}%`,
          avgDuration: `${(metrics.avgDuration / 1000 / 60).toFixed(2)} minutes`,
          totalDuration: `${(metrics.totalDuration / 1000 / 60).toFixed(2)} minutes`,
          pipelineMetrics,
        };
      });

      const totalRunsAllDays = formattedDailyMetrics.reduce((sum, day) => sum + day.totalRuns, 0);
      const totalSuccessfulRuns = formattedDailyMetrics.reduce((sum, day) => sum + day.successfulRuns, 0);
      const totalFailedRuns = formattedDailyMetrics.reduce((sum, day) => sum + day.failedRuns, 0);
      const totalDurationAllDays = formattedDailyMetrics.reduce((sum, day) => sum + parseFloat(day.totalDuration) * 60 * 1000, 0);

      const overallSuccessRate = totalRunsAllDays > 0 ? (totalSuccessfulRuns / totalRunsAllDays) * 100 : 0;

      const overallMetrics = {
        startTime,
        endTime,
        totalDays: dateRange.length,
        totalRuns: totalRunsAllDays,
        successfulRuns: totalSuccessfulRuns,
        failedRuns: totalFailedRuns,
        successRate: `${overallSuccessRate.toFixed(2)}%`,
        avgDuration: totalRunsAllDays > 0 ? `${(totalDurationAllDays / totalRunsAllDays / 1000 / 60).toFixed(2)} minutes` : "0 minutes",
        totalDuration: `${(totalDurationAllDays / 1000 / 60).toFixed(2)} minutes`,
      };

      return {
        jsonBody: {
          overallMetrics,
          dailyMetrics: formattedDailyMetrics,
        },
      };
    } catch (error) {
      context.log(`Error in getDailyPipelineMetrics: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Failed to retrieve daily pipeline metrics",
          message: error.message,
        }),
      };
    }
  },
});

app.http("getPipelineSchedules", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "pipeline-schedules",
  handler: async (request, context) => {
    try {
      let requestBody = {};
      try {
        const body = await request.text();
        if (body) requestBody = JSON.parse(body);
      } catch (e) {
        context.log(`Error parsing request: ${e.message}`);
      }

      const { pipelineName } = requestBody;

      const allTriggers = [];
      const triggerIterator = client.triggerOperations.listTriggersByWorkspace();

      for await (const triggerPage of triggerIterator.byPage()) {
        for (const trigger of triggerPage) {
          try {
            const triggerDetails = await client.triggerOperations.getTrigger(trigger.name);

            const associatedPipelines = (trigger.properties.pipelines || []).map((p) => p.pipelineReference.referenceName);

            if (pipelineName && !associatedPipelines.includes(pipelineName)) continue;

            const triggerInfo = {
              name: trigger.name,
              id: trigger.id,
              type: trigger.properties.type,
              status: triggerDetails.properties?.runtimeState || "Unknown",
              description: trigger.properties.description,
              properties: trigger.properties,
            };

            allTriggers.push(triggerInfo);
          } catch (triggerError) {
            context.log(`Error processing trigger ${trigger.name}: ${triggerError.message}`);

            allTriggers.push({
              name: trigger.name,
              id: trigger.id,
              type: trigger.properties.type,
              status: "Error",
              error: triggerError.message,
            });
          }
        }
      }

      return {
        jsonBody: {
          schedules: allTriggers,
          count: allTriggers.length,
        },
      };
    } catch (error) {
      context.log(`Error in getPipelineSchedules: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Failed to retrieve pipeline schedules",
          message: error.message,
        }),
      };
    }
  },
});

// sample body
// {
//   "triggerName": "BusinessHoursTrigger",
//   "scheduleConfig": {
//     "recurrence": {
//       "frequency": "Week",
//       "interval": 1,
//       "startTime": "2023-05-01T12:45:00Z",
//       "timeZone": "America/Chicago",
//       "schedule": {
//         "weekDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
//         "hours": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
//         "minutes": [45]
//       }
//     }
//   }
// }
app.http("updateTriggerSchedule", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "update-trigger-schedule",
  handler: async (request, context) => {
    try {
      const body = await request.text();
      const { triggerName, scheduleConfig } = JSON.parse(body);

      if (!triggerName) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Missing required parameter",
            message: "triggerName is required",
          }),
        };
      }

      context.log(scheduleConfig);

      if (!scheduleConfig || !scheduleConfig.recurrence) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Missing required parameter",
            message: "scheduleConfig with recurrence is required",
          }),
        };
      }

      try {
        // First, get the current trigger to preserve its other properties
        const currentTrigger = await client.triggerOperations.getTrigger(triggerName);

        if (currentTrigger.properties.type !== "ScheduleTrigger") {
          return {
            status: 400,
            body: JSON.stringify({
              error: "Invalid trigger type",
              message: `Cannot update schedule for trigger of type '${currentTrigger.properties.type}'. Only ScheduleTrigger is supported.`,
            }),
          };
        }

        const isCurrentlyStarted = currentTrigger.properties.runtimeState === "Started";

        // If the trigger is currently running, stop it first
        if (isCurrentlyStarted) {
          await client.triggerOperations.beginStopTriggerAndWait(triggerName);
        }

        // Create a new trigger resource object with the correct structure
        const updatedTrigger = {
          id: currentTrigger.id,
          name: currentTrigger.name,
          type: currentTrigger.type,
          properties: {
            type: "ScheduleTrigger",
            pipelines: currentTrigger.properties.pipelines || [],
            annotations: currentTrigger.properties.annotations || [],
            recurrence: scheduleConfig.recurrence,
          },
        };

        context.log("Updating trigger with:", JSON.stringify(updatedTrigger, null, 2));

        // Update the trigger
        const poller = await client.triggerOperations.beginCreateOrUpdateTrigger(triggerName, updatedTrigger);

        const result = await poller.pollUntilDone();

        // Start the trigger if it was previously started
        if (isCurrentlyStarted) {
          await client.triggerOperations.beginStartTriggerAndWait(triggerName);
        }

        // Fetch the updated trigger to verify changes
        const finalTrigger = await client.triggerOperations.getTrigger(triggerName);

        return {
          jsonBody: {
            triggerName,
            message: "Trigger schedule updated successfully",
            updatedTrigger: finalTrigger,
          },
        };
      } catch (triggerError) {
        context.log(`Error updating trigger: ${triggerError.message}`);
        if (triggerError.stack) {
          context.log(`Stack trace: ${triggerError.stack}`);
        }
        return {
          status: 500,
          body: JSON.stringify({
            error: "Failed to update trigger",
            message: triggerError.message,
          }),
        };
      }
    } catch (error) {
      context.log(`Error in updateTriggerSchedule: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Something went wrong!",
          message: error.message,
        }),
      };
    }
  },
});

// start a trigger
app.http("startTrigger", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "start-trigger",
  handler: async (request, context) => {
    try {
      const body = await request.text();
      const { triggerName } = JSON.parse(body);

      const currentTrigger = await client.triggerOperations.getTrigger(triggerName);

      const isCurrentlyStarted = currentTrigger.properties.runtimeState === "Started";

      if (isCurrentlyStarted) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Trigger is already started",
            message: "Trigger is already started",
          }),
        };
      }

      await client.triggerOperations.beginStartTriggerAndWait(triggerName);

      return {
        jsonBody: {
          triggerName,
          message: "Trigger started successfully",
        },
      };
    } catch (error) {
      context.log(`Error in startTrigger: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Something went wrong!",
          message: error.message,
        }),
      };
    }
  },
});

// stop a trigger
app.http("stopTrigger", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "stop-trigger",
  handler: async (request, context) => {
    try {
      const body = await request.text();
      const { triggerName } = JSON.parse(body);

      const currentTrigger = await client.triggerOperations.getTrigger(triggerName);

      const isCurrentlyStarted = currentTrigger.properties.runtimeState === "Stopped";

      if (isCurrentlyStarted) {
        return {
          status: 400,
          body: JSON.stringify({
            error: "Trigger is already stopped",
            message: "Trigger is already stopped",
          }),
        };
      }

      await client.triggerOperations.beginStopTriggerAndWait(triggerName);

      return {
        jsonBody: {
          triggerName,
          message: "Trigger stopped successfully",
        },
      };
    } catch (error) {
      context.log(`Error in stopTrigger: ${error.message}`);
      return {
        status: 500,
        body: JSON.stringify({
          error: "Something went wrong!",
          message: error.message,
        }),
      };
    }
  },
});
