const { app } = require("@azure/functions");
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");
const CONFIG = require("./config");
const { executeQuery } = require("./helper/helper");
const { ROLES } = require("./helper/constants");
const moment = require("moment-timezone");
const { authenticate } = require("./helper/auth");
const { withLogging } = require("./helper/loggingMiddleware");

const credential = new DefaultAzureCredential();
const vaultName = process.env["KEY_VAULT_NAME"];
const url = `https://${vaultName}.vault.azure.net`;
const client = new SecretClient(url, credential);

app.http("getRevelDimDiscountCategory", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "get-revel-dim-discount-category",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.DISCOUNT_PAGE_ADMIN, ROLES.DISCOUNT_PAGE_READER]);

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.Revel.Dim_Discount_Category
        `;

      context.log("sqlQuery", sqlQuery);
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName: `${CONFIG.dbName}.Revel.Dim_Discount_Category`,
          count: result.length,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error, sqlQuery },
      };
    }
  },
});

// [
//   {
//     "discount_name": "Discount 1",
//     "gl_account_no": "GL001",
//     "is_active": true,
//     "source": "Revel"
//   },
//   {
//     "discount_name": "Discount 2",
//     "gl_account_no": "GL002",
//     "is_active": false,
//     "source": "Manual"
//   }
// ]

// - [ ] For Discount GL code
app.http("addRevelDiscountGLCode", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "add-revel-discount-gl-code",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.DISCOUNT_PAGE_ADMIN]);
      // const SOURCE = "Revel";

      // Changed to accept an array of records
      const records = await request.json();

      // Validate input is an array
      if (!Array.isArray(records)) {
        return {
          status: 400,
          jsonBody: { error: "Request body must be an array of discount records" },
        };
      }

      // Validate each record
      for (const record of records) {
        if (!record.discount_name || !record.gl_account_no || !record.source) {
          return {
            status: 400,
            jsonBody: {
              error: "Each record must contain: discount_name, gl_account_no, is_active, and source",
            },
          };
        }
      }

      const currentDate = moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss");

      // Create VALUES clause for multiple records
      const values = records
        .map((_, index) => `(@discount_name${index}, @gl_account_no${index}, @updateDate, @isActive${index}, @source${index})`)
        .join(", ");

      sqlQuery = `
        INSERT INTO ${CONFIG.dbName}.Revel.Dim_Discount_Category
        (discount_name, gl_account_no, Updated_date, is_active, source)
        VALUES ${values}
      `;

      // Create params object with indexed parameters
      const params = records.reduce(
        (acc, record, index) => ({
          ...acc,
          [`discount_name${index}`]: record.discount_name,
          [`gl_account_no${index}`]: record.gl_account_no,
          [`isActive${index}`]: record.is_active,
          [`source${index}`]: record.source,
        }),
        {
          updateDate: currentDate,
        }
      );

      context.log("sqlQuery", sqlQuery);
      context.log("params", params);

      const result = await executeQuery(sqlQuery, params);

      setLoggingData(tokenResult, sqlQuery);

      return {
        status: 200,
        jsonBody: {
          message: `${records.length} discount GL code(s) added successfully`,
          sqlQuery,
          result,
          tableName: `${CONFIG.dbName}.Revel.Dim_Discount_Category`,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});

// - [ ] They can only edit the GL account number
app.http("updateRevelDiscountGLCode", {
  methods: ["PUT"],
  authLevel: "anonymous",
  route: "update-revel-discount-gl-code",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.DISCOUNT_PAGE_ADMIN]);

      // Changed to use id instead of glAccountNo
      const { id, newGlAccountNo, newDiscountName, source } = await request.json();

      // Validate required fields
      if (!id) {
        return {
          status: 400,
          jsonBody: { error: "Record ID is required" },
        };
      }

      const currentDate = moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss");

      const updateFields = [];
      const params = {
        id, // Changed parameter name
        updateDate: currentDate,
        is_active: true,
      };

      if (newGlAccountNo) {
        updateFields.push("gl_account_no = @newGlAccountNo");
        params.newGlAccountNo = newGlAccountNo;
      }

      if (newDiscountName) {
        updateFields.push("discount_name = @newDiscountName");
        params.newDiscountName = newDiscountName;
      }

      // if (typeof is_active !== "undefined") {
      //   updateFields.push("is_active = @is_active");
      //   params.is_active = true;
      // }

      if (source) {
        updateFields.push("source = @source");
        params.source = source;
      }

      updateFields.push("Updated_date = @updateDate");

      sqlQuery = `
        UPDATE ${CONFIG.dbName}.Revel.Dim_Discount_Category
        SET ${updateFields.join(", ")}
        WHERE id = @id
      `;

      context.log("sqlQuery", sqlQuery);
      context.log("params", params);

      const result = await executeQuery(sqlQuery, params);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, sqlQuery);

      if (result.rowsAffected === 0) {
        return {
          status: 404,
          jsonBody: { error: "Record ID not found" },
        };
      }

      return {
        status: 200,
        jsonBody: {
          message: "Discount information updated successfully",
          sqlQuery,
          result,
          tableName: `${CONFIG.dbName}.Revel.Dim_Discount_Category`,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});

// Get discounts with null GL account numbers
app.http("getRevelGLDiscounts", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-revel-gl-discounts",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.DISCOUNT_PAGE_ADMIN, ROLES.DISCOUNT_PAGE_READER]);

      const body = await request.json();
      const { page = 1, limit = 200, searchColumns = [], searchText = "", sortBy = "order_date", sortOrder = "desc" } = body;

      const offset = (page - 1) * limit;

      // Build where clause
      let whereClause = "WHERE gl_account_no IS NULL";
      const params = {};

      // Add search functionality if both searchColumns and searchText are provided
      if (searchText && searchColumns && Array.isArray(searchColumns) && searchColumns.length > 0) {
        const searchConditions = searchColumns.map((column, index) => {
          params[`searchText${index}`] = `%${searchText}%`;
          return `${column} LIKE @searchText${index}`;
        });

        if (searchConditions.length > 0) {
          whereClause += ` AND (${searchConditions.join(" OR ")})`;
        }
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*) AS count 
        FROM ${CONFIG.dbName}.Revel.VW_GP_Sales_Discounts 
        ${whereClause}
      `;

      context.log("countQuery", countQuery);
      const countResult = await executeQuery(countQuery, params);
      const totalCount = countResult[0].count;
      const totalPages = Math.ceil(totalCount / limit);

      // Validate sort field
      const allowedSortFields = ["order_date", "store_name", "discount_name", "gl_account_no", "source"];
      const validSortField = allowedSortFields.includes(sortBy) ? sortBy : "order_date";
      const validSortOrder = sortOrder.toLowerCase() === "asc" ? "ASC" : "DESC";

      // Main query
      sqlQuery = `
        SELECT order_date, store_name, discount_name, gl_account_no, source
        FROM ${CONFIG.dbName}.Revel.VW_GP_Sales_Discounts 
        ${whereClause}
        ORDER BY ${validSortField} ${validSortOrder}
        OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY
      `;

      context.log("sqlQuery", sqlQuery);
      context.log("params", params);
      const result = await executeQuery(sqlQuery, params);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName: `${CONFIG.dbName}.Revel.VW_GP_Sales_Discounts`,
          limit,
          currentPage: page,
          totalPages,
          totalCount,
          count: result.length,
          sortBy: validSortField,
          sortOrder: validSortOrder,
          searchColumns,
          searchText,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  },
});
