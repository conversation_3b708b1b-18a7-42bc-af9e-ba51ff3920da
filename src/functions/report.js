const { app } = require("@azure/functions");
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");
const CONFIG = require("./config");
const { executeQuery } = require("./helper/helper");
const { authenticate } = require("./helper/auth");
const moment = require("moment-timezone");
const xlsx = require("xlsx");
const { ROLES, allowedFields } = require("./helper/constants");

// get report from SQL DB
app.http("getReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { fiscalYear = 2024, fiscalMonth = new Date().getMonth() + 1 } = body ? JSON.parse(body) : {};

      if (fiscalMonth && fiscalMonth > 12) {
        throw new Error("Invalid fiscalMonth");
      }

      sqlQuery = `
      SELECT
        order_date,
        storeConceptName,
        previousYearDate,
        SUM(net_sales) AS net_sales,
        SUM(transactions) AS transactions,
        SUM(previousYear_NetSales) AS PreviousYear_NetSales,
        SUM(PreviousYear_Transactions) AS PreviousYearTransactions,
        COUNT(DISTINCT store_name) AS store_count
    FROM
    ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales
    WHERE
        fiscalYear = ${fiscalYear}
        AND fiscalPeriodNumber = ${fiscalMonth}
    GROUP BY
        order_date,
        storeConceptName,
        previousYearDate;
    `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// get cohort report
app.http("getCohortReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-cohort-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      const body = await request.text();

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Cohort
      `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// get geography report
app.http("getGeographyReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-geography-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      const body = await request.text();

      sqlQuery = `
      SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Geography
      `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// get region report
app.http("getRegionReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      const body = await request.text();

      sqlQuery = `
      SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Region
      `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// get district report
app.http("getDistrictReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-district-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      const body = await request.text();

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Region_District
      `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// get segment report
app.http("getSegmentReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-segment-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      const body = await request.text();

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Segment
      `;

      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// dbo.USP_Daily_Comp_Store_Sales_Segment 'PTD'
// dbo.USP_Daily_Comp_Store_Sales_Segment 'YTD'
// dbo.USP_Daily_Comp_Store_Sales_Region_District 'PTD'
// dbo.USP_Daily_Comp_Store_Sales_Region_District 'YTD'
// dbo.USP_Daily_Comp_Store_Sales_Region_PTD_YTD 'PTD'
// dbo.USP_Daily_Comp_Store_Sales_Region_PTD_YTD 'YTD'
// dbo.USP_Daily_Comp_Store_Sales_Geography 'PTD'
// dbo.USP_Daily_Comp_Store_Sales_Geography 'YTD'
// SP_Comp_Store_Sales_Cohort 'PTD';
// SP_Comp_Store_Sales_Cohort 'YTD';

// Combined Store Sales Reports API
// Segment Sales Report API
app.http("getSegmentSalesReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-segment-ty-report",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { type = "PTD" } = body ? JSON.parse(body) : {};

      if (!["PTD", "YTD"].includes(type)) {
        throw new Error("Invalid type. Must be 'PTD' or 'YTD'.");
      }

      const query = `EXEC dbo.USP_Daily_Comp_Store_Sales_Segment '${type}'`;
      const result = await executeQuery(query);

      return {
        status: 200,
        jsonBody: {
          segmentSales: result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Region District Sales Report API
app.http("getRegionDistrictSalesReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-district-ty-report",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { type = "PTD" } = body ? JSON.parse(body) : {};

      if (!["PTD", "YTD"].includes(type)) {
        throw new Error("Invalid type. Must be 'PTD' or 'YTD'.");
      }

      const query = `EXEC dbo.USP_Daily_Comp_Store_Sales_Region_District '${type}'`;
      const result = await executeQuery(query);

      return {
        status: 200,
        jsonBody: {
          regionDistrictSales: result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Region Sales Report API
app.http("getRegionSalesReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-ty-report",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { type = "PTD" } = body ? JSON.parse(body) : {};

      if (!["PTD", "YTD"].includes(type)) {
        throw new Error("Invalid type. Must be 'PTD' or 'YTD'.");
      }

      const query = `EXEC dbo.USP_Daily_Comp_Store_Sales_Region_PTD_YTD '${type}'`;
      const result = await executeQuery(query);

      return {
        status: 200,
        jsonBody: {
          regionSales: result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Geography Sales Report API
app.http("getGeographySalesReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-geography-ty-report",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { type = "PTD" } = body ? JSON.parse(body) : {};

      if (!["PTD", "YTD"].includes(type)) {
        throw new Error("Invalid type. Must be 'PTD' or 'YTD'.");
      }

      const query = `EXEC dbo.USP_Daily_Comp_Store_Sales_Geography '${type}'`;
      const result = await executeQuery(query);

      return {
        status: 200,
        jsonBody: {
          geographySales: result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Cohort Sales Report API
app.http("getCohortSalesReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-cohort-ty-report",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { type = "PTD" } = body ? JSON.parse(body) : {};

      if (!["PTD", "YTD"].includes(type)) {
        throw new Error("Invalid type. Must be 'PTD' or 'YTD'.");
      }

      const query = `EXEC SP_Comp_Store_Sales_Cohort '${type}'`;
      const result = await executeQuery(query);

      return {
        status: 200,
        jsonBody: {
          cohortSales: result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Week To Date Reports
// VW_Daily_Comp_Store_Sales_Cohort_PYWTD
// VW_Daily_Comp_Store_Sales_Geography_PYWTD
// VW_Daily_Comp_Store_Sales_Region_District_PYWTD
// VW_Daily_Comp_Store_Sales_Region_PYWTD
// VW_Daily_Comp_Store_Sales_Segment_PYWTD
// these views are for week to date section

// Cohort WTD Report
app.http("getCohortLYReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-cohort-ly-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Cohort_PYWTD`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Geography LY Report
app.http("getGeographyLYReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-geography-ly-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Geography_PYWTD`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region District LY Report
app.http("getRegionDistrictLYReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-district-ly-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Region_District_PYWTD`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region LY Report
app.http("getRegionLYReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-ly-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Region_PYWTD`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Segment LY Report
app.http("getSegmentLYReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-segment-ly-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `SELECT * FROM ${CONFIG.dbName}.${CONFIG.schema}.VW_Daily_Comp_Store_Sales_Segment_PYWTD`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// SP_Comp_Store_Sales_Cohort_PY 'PTD';
// SP_Comp_Store_Sales_Cohort_PY 'YTD';

// SP_Daily_Comp_Store_Sales_Geography_PY 'PTD';
// SP_Daily_Comp_Store_Sales_Geography_PY 'YTD';

// SP_Daily_Comp_Store_Sales_Region_PY 'PTD';
// SP_Daily_Comp_Store_Sales_Region_PY 'YTD';

// SP_Daily_Comp_Store_Sales_Region_District_PY 'PTD';
// SP_Daily_Comp_Store_Sales_Region_District_PY 'YTD';

// SP_Daily_Comp_Store_Sales_Segment_PY 'PTD';
// SP_Daily_Comp_Store_Sales_Segment_PY 'YTD';

// Cohort PTD Report
app.http("getCohortPTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-cohort-ly-ptd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Comp_Store_Sales_Cohort_PY 'PTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Cohort YTD Report
app.http("getCohortYTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-cohort-ly-ytd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Comp_Store_Sales_Cohort_PY 'YTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Geography PTD Report
app.http("getGeographyPTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-geography-ly-ptd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Geography_PY 'PTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Geography YTD Report
app.http("getGeographyYTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-geography-ly-ytd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Geography_PY 'YTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region PTD Report
app.http("getRegionPTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-ly-ptd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Region_PY 'PTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region YTD Report
app.http("getRegionYTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-ly-ytd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Region_PY 'YTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region District PTD Report
app.http("getRegionDistrictPTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-district-ly-ptd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Region_District_PY 'PTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Region District YTD Report
app.http("getRegionDistrictYTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-region-district-ly-ytd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Region_District_PY 'YTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Segment PTD Report
app.http("getSegmentPTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-segment-ly-ptd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Segment_PY 'PTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});

// Segment YTD Report
app.http("getSegmentYTDReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-segment-ly-ytd-report",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      sqlQuery = `EXEC ${CONFIG.dbName}.${CONFIG.schema}.SP_Daily_Comp_Store_Sales_Segment_PY 'YTD'`;
      const result = await executeQuery(sqlQuery);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, query: sqlQuery },
      };
    }
  },
});
