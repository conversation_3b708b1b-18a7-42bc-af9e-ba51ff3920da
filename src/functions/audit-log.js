const { app } = require("@azure/functions");
const { ROLES } = require("./helper/constants");
const { authenticate } = require("./helper/auth");
const { fetchAuditLogs } = require("./helper/cosmos");

app.http("getAuditLogs", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-audit-logs",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const body = await request.text();
      const { pageSize = 50, pageNumber = 1, searchTerm = null } = body ? JSON.parse(body) : {};

      const { items, totalCount, currentPage, totalPages, hasMoreResults } = await fetchAuditLogs(
        {
          pageSize,
          pageNumber,
          searchTerm,
        },
        context
      );

      return {
        status: 200,
        jsonBody: {
          totalCount,
          currentPage,
          totalPages,
          hasMoreResults,
          pageSize,
          result: items,
          filters: {
            searchTerm,
          },
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: error.status || 500,
        jsonBody: { error: error.message },
      };
    }
  },
});
