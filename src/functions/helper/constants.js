const ROLES = {
  ADMIN: "Admin",
  DEVELOPER: "Developer",
  READER: "Reader",
  STORE_LOGISTIC_ADMIN: "Store-Logistic-Admin",
  STORE_LOGISTIC_READER: "Store-Logistic-Reader",
  DISCOUNT_PAGE_ADMIN: "Discount-Page-Admin",
  DISCOUNT_PAGE_READER: "Discount-Page-Reader",
};

const allowedFields = [
  "SQL ID",
  "storeName",
  "Store Number",
  "State",
  "Store Code",
  "Legacy Store ID",
  "Store",
  "Phone",
  "siteAddress1",
  "siteAddress2",
  "city",
  "stateAbbreviation",
  "zip",
  "storeConceptName",
  "Open Year",
  "openDate",
  "marketName",
  "geographyName",
  "Region",
  "District",
];

const allowedTables = [
  "str_store",
  "str_site",
  "str_storeConcept",
  "str_district",
  "str_market",
  "str_regionDistrict",
  "str_region",
  // "emp_employee",
  "district_employee",
  "region_employee",
  "str_geography",
  "emp_job",
  "emp_department",
];

const queryTables = [
  "str_store",
  "str_site",
  "str_storeConcept",
  "str_district",
  "str_market",
  "str_regionDistrict",
  "str_region",
  "emp_employee",
  "str_geography",
  "emp_job",
  "emp_department",
  "hst_employeeHistory",
];

const sortFieldMapping = {
  "Site Number": "SQL ID",
  "Store Name": "storeName",
  "Store Number": "Store Number",
  State: "State",
  "Store Code": "Store Code",
  "Legacy Store ID": "Legacy Store ID",
  Store: "Store",
  Phone: "Phone",
  "Site Address 1": "siteAddress1",
  "Site Address 2": "siteAddress2",
  City: "city",
  "State Abbreviation": "stateAbbreviation",
  Zip: "zip",
  "Store Concept Name": "storeConceptName",
  "Open Year": "Open Year",
  "Open Date": "openDate",
  "Market Name": "marketName",
  "Geography Name": "geographyName",
  Region: "Region",
  District: "District",
  RM: "RM",
  DM: "DM",
};

module.exports = { ROLES, allowedFields, allowedTables, queryTables, sortFieldMapping };
