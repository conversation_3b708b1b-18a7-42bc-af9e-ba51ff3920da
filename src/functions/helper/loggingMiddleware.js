const moment = require("moment-timezone");
const { pushLogToCosmosDb } = require("./cosmos");

const createLogObject = (request, tokenResult, sqlQuery, result, context) => {
  const apiEndpoint = request.url || null;
  const timestamp = moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss");
  let requestBody = null;

  try {
    // Only parse if content exists and is JSON
    if (request.body && request.headers?.get("content-type")?.includes("application/json")) {
      requestBody = request.body;
    }
  } catch (error) {
    context.log("Error parsing request body:", error);
  }

  return {
    userId: tokenResult?.oid || "anonymous",
    name: tokenResult?.name || "unknown",
    roles: tokenResult?.roles || [],
    email: tokenResult?.upn || "unknown",
    ipAddress: tokenResult?.ipaddr || "unknown",
    tenantId: tokenResult?.tid || "unknown",
    apiEndpoint,
    requestBody,
    result: result?.length > 0 ? result?.length > 0 : null,
    sqlQuery: Array.isArray(sqlQuery) ? sqlQuery : sqlQuery || null,
    timestamp,
  };
};

const withLogging = (handler) => {
  return async (request, context) => {
    let result = null;
    let tokenResult = null;
    let sqlQuery = null;

    try {
      // Execute the original handler
      const response = await handler(request, context, (tr, sq) => {
        tokenResult = tr;
        sqlQuery = sq;
      });
      result = response?.jsonBody?.result;

      // Create and push log object
      const logObject = createLogObject(request, tokenResult, sqlQuery, result, context);
      await pushLogToCosmosDb(logObject, context);

      // // Remove sqlQuery from response if user is not a Developer
      // if (response?.jsonBody && !tokenResult?.roles?.includes("Developer")) {
      //   delete response.jsonBody.sqlQuery;
      // }

      return response;
    } catch (error) {
      // Log error scenario
      const logObject = createLogObject(request, tokenResult, sqlQuery, result, context);
      logObject.error = error.message;
      await pushLogToCosmosDb(logObject, context);

      throw error;
    }
  };
};

module.exports = { withLogging };
