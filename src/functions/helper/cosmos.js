const { CosmosClient } = require("@azure/cosmos");

// Constants for database configuration
const COSMOS_DB_CONNECTION_STRING = process.env["AUDIT_LOG_DB_CONNECTION_STRING"];
const COSMOS_DB_DATABASE = "AUDIT_LOG_DB_DATABASE";
const COSMOS_DB_CONTAINER = "AUDIT_LOG_DB_CONTAINER";

// Reusable client instance to avoid creating new connections
let cosmosClient = null;

if (!COSMOS_DB_CONNECTION_STRING) {
  throw new Error("AUDIT_LOG_DB_CONNECTION_STRING environment variable is not configured");
}

/**
 * Pushes a log object to Cosmos DB
 * @param {Object} logObject - The log data to store
 * @param {Object} context - The context object
 * @throws {Error} If connection or write fails
 */
const pushLogToCosmosDb = async (logObject, context) => {
  try {
    // Initialize client if not exists
    if (!cosmosClient) {
      cosmosClient = new CosmosClient(COSMOS_DB_CONNECTION_STRING);
    }

    const database = cosmosClient.database(COSMOS_DB_DATABASE);
    const container = database.container(COSMOS_DB_CONTAINER);

    await container.items.create(logObject);
  } catch (error) {
    context.log("Failed to write to Cosmos DB:", {
      error: error.message,
      stack: error.stack,
      logObject: JSON.stringify(logObject),
    });
    throw new Error(`Database write operation failed: ${error.message}`, { cause: error });
  }
};

/**
 * Fetches audit logs with pagination and filtering
 * @param {Object} options - Query options
 * @param {number} options.pageSize - Number of items per page
 * @param {number} options.pageNumber - Page number
 * @param {string} options.searchTerm - Search term
 * @param {Object} context - The context object
 * @returns {Promise<{items: Array, totalCount: number, currentPage: number, totalPages: number, hasMoreResults: boolean}>}
 */
const fetchAuditLogs = async ({ pageSize = 50, pageNumber = 1, searchTerm = null }, context) => {
  try {
    if (!cosmosClient) {
      cosmosClient = new CosmosClient(COSMOS_DB_CONNECTION_STRING);
    }

    const database = cosmosClient.database(COSMOS_DB_DATABASE);
    const container = database.container(COSMOS_DB_CONTAINER);

    let whereClause = "";
    const parameters = [];

    if (searchTerm) {
      whereClause = `WHERE CONTAINS(LOWER(c.name), LOWER(@searchTerm)) OR 
                           CONTAINS(LOWER(c.email), LOWER(@searchTerm)) OR 
                           CONTAINS(LOWER(c.apiEndpoint), LOWER(@searchTerm))`;
      parameters.push({ name: "@searchTerm", value: searchTerm });
    }

    const countQuerySpec = {
      query: `SELECT VALUE COUNT(1) FROM c ${whereClause}`,
      parameters: parameters,
    };
    const countResponse = await container.items.query(countQuerySpec).fetchAll();
    const totalCount = countResponse.resources[0];

    const offset = (pageNumber - 1) * pageSize;
    const querySpec = {
      query: `SELECT * FROM c ${whereClause} ORDER BY c.timestamp DESC OFFSET ${offset} LIMIT ${pageSize}`,
      parameters: parameters,
    };

    const { resources: items } = await container.items.query(querySpec).fetchAll();

    return {
      items,
      totalCount,
      currentPage: pageNumber,
      totalPages: Math.ceil(totalCount / pageSize),
      hasMoreResults: offset + items.length < totalCount,
    };
  } catch (error) {
    context.log("Failed to fetch audit logs:", {
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to fetch audit logs: ${error.message}`, { cause: error });
  }
};

module.exports = { pushLogToCosmosDb, fetchAuditLogs };
