const { verify } = require("azure-ad-jwt");

const authenticate = async (request, context, allowedRoles = []) => {
  const authorizationHeader = request.headers.get("authorization");
  if (!authorizationHeader) {
    throw new Error("Unauthorized");
  }

  const token = authorizationHeader.split(" ")[1];
  try {
    const tokenResult = await new Promise((resolve, reject) => {
      verify(token, null, function (err, result) {
        if (result) {
          resolve(result);
        } else {
          context.log(`Token validation failed: ${err}`);
          reject(err);
        }
      });
    });

    const roles = tokenResult?.roles || [];
    if (!roles.some((role) => allowedRoles.includes(role))) {
      throw new Error("Forbidden");
    }

    return tokenResult;
  } catch (error) {
    context.log(`Token validation failed: ${error.message}`);
    throw new Error("Unauthorized");
  }
};

module.exports = { authenticate };
