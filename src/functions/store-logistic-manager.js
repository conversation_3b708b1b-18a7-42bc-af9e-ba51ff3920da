const { app } = require("@azure/functions");
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");
const CONFIG = require("./config");
const { executeQueryStoreLogisticsManager, executeTransaction, executeQuery } = require("./helper/helper");
const { ROLES, allowedTables, queryTables, sortFieldMapping } = require("./helper/constants");
const moment = require("moment-timezone");
const { authenticate } = require("./helper/auth");
const { withLogging } = require("./helper/loggingMiddleware");

const IDENTITY_KEYS = {
  str_geography: ["geographyId", "validFrom", "validTo"],
  str_store: ["storeId", "validFrom", "validTo"],
  emp_employee: ["employeeId", "validFrom", "validTo"],
  str_district: ["districtid", "validFrom", "validTo"],
  str_market: ["marketId", "validFrom", "validTo"],
  str_region: ["regionid", "validFrom", "validTo"],
  str_regionDistrict: ["regionDistrictId", "validFrom", "validTo"],
  str_storeConcept: ["storeConceptId", "validFrom", "validTo"],
  str_site: ["siteId", "validFrom", "validTo"],
  emp_job: ["jobId", "validFrom", "validTo"],
  emp_department: ["departmentId", "validFrom", "validTo"],
};

const PRIMARY_KEYS = {
  str_geography: ["geographyId", "validFrom", "validTo"],
  str_store: [
    "storeId",
    "siteId",
    "storeNumber",
    "storeName",
    // "districtId",
    "storeConceptId",
    "marketId",
    "geographyId",
    "legacyStoreCode",
    "legacyStoreId",
    "validFrom",
    "validTo",
  ],
  emp_employee: ["employeeId", "employeeNumber", "validFrom", "validTo"],
  str_district: ["districtid", "validFrom", "validTo"],
  str_market: ["marketId", "validFrom", "validTo"],
  str_region: ["regionid", "validFrom", "validTo"],
  str_regionDistrict: ["regionDistrictId", "validFrom", "validTo"],
  str_storeConcept: ["storeConceptId", "validFrom", "validTo"],
  str_site: ["siteId", "siteName", "validFrom", "validTo"],
  emp_job: ["jobId", "validFrom", "validTo"],
  emp_department: ["departmentId", "validFrom", "validTo"],
};

const NOT_NULL = {
  str_geography: ["geographyName"],
  str_store: ["companyId", "siteId", "posId", "loyaltyId", "storeConceptId", "storeNumber", "storeName", "showReporting", "compareDate", "IsClosed"],
  emp_employee: ["companyId", "employeeNumber", "firstName", "lastName", "jobId", "isActive", "managerEmployeeNumber"],
  str_district: ["districtName", "employeeId"],
  str_market: ["marketName"],
  str_region: ["regionName", "employeeId"],
  str_regionDistrict: ["regionId", "districtId"],
  str_storeConcept: ["storeConceptName"],
  str_site: ["siteAddress1", "city", "state", "stateAbbreviation", "zip", "siteName"],
  emp_job: ["jobDescription"],
  emp_department: ["departmentName"],
};

const credential = new DefaultAzureCredential();
const vaultName = process.env["KEY_VAULT_NAME"];
const url = `https://${vaultName}.vault.azure.net`;
const client = new SecretClient(url, credential);

// get all stores list from SQL DB with pagination
app.http("getAllStores", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-stores",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
      const body = await request.text();

      const { page = 1, limit = 200, filters = {}, sortBy = "Site Number", sortOrder = "asc" } = body ? JSON.parse(body) : {};
      const offset = (page - 1) * limit;

      let whereClause = "";
      const filterFields = [
        "SQL ID",
        "storeName",
        "Store Number",
        "State",
        "Store Code",
        "Legacy Store ID",
        "Store",
        "Phone",
        "siteAddress1",
        "siteAddress2",
        "city",
        "stateAbbreviation",
        "zip",
        "storeConceptName",
        "Open Year",
        "openDate",
        "marketName",
        "geographyName",
        "Region",
        "District",
        "IsClosed",
      ];

      const filterConditions = [];

      // Process all filters except IsClosed
      filterFields
        .filter((field) => field !== "IsClosed" && filters[field])
        .forEach((field) => {
          let values = filters[field];
          if (typeof values === "string") {
            values = values.split(",").map((v) => v.trim());
          } else if (!Array.isArray(values)) {
            values = [values];
          }
          filterConditions.push(`${field} IN (${values.map((v) => `'${v}'`).join(", ")})`);
        });

      // Handle IsClosed as a boolean (coming as string "true" or "false")
      if (filters.IsClosed !== undefined) {
        const isClosedValue = filters.IsClosed === "true" || filters.IsClosed === true ? 1 : 0;
        filterConditions.push(`IsClosed = ${isClosedValue}`);
      }

      if (filterConditions.length > 0) {
        whereClause = `WHERE ${filterConditions.join(" AND ")}`;
      }

      const totalCountQuery = `SELECT COUNT(*) AS count FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.storeLogisticsManagerTableName} ${whereClause}`;
      const totalCountResult = await executeQueryStoreLogisticsManager(totalCountQuery);
      const totalCount = totalCountResult[0].count;
      const totalPages = Math.ceil(totalCount / limit);

      const validSortFields = Object.values(sortFieldMapping);
      const mappedSortField = sortFieldMapping[sortBy] || sortBy;
      const sortField = validSortFields.includes(mappedSortField) ? mappedSortField : "Store Number";
      const sortDirection = sortOrder.toLowerCase() === "desc" ? "DESC" : "ASC";

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.storeLogisticsManagerTableName}
        ${whereClause}
        ORDER BY [${sortField}] ${sortDirection}
        OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

      context.log("sqlQuery", sqlQuery);
      const result = await executeQueryStoreLogisticsManager(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName: CONFIG.storeLogisticsManagerTableName,
          limit,
          currentPage: page,
          totalPages,
          totalCount,
          count: result.length,
          sortBy: sortBy,
          sortOrder: sortDirection,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error, sqlQuery },
      };
    }
  },
});

// to get single store tables data
app.http("getTableView", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-table-view",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
      const body = await request.text();
      const { tableName, searchColumns, searchText, page = 1, limit = 10 } = body ? JSON.parse(body) : {};

      context.log("Received request body:", { tableName, searchColumns, searchText, page, limit });

      if (!tableName) {
        return {
          status: 400,
          jsonBody: { error: "tableName is required" },
        };
      }

      if (!queryTables.includes(tableName)) {
        return {
          status: 400,
          jsonBody: {
            error: `Invalid table name: ${tableName}`,
            queryTables: queryTables.join(", "),
          },
        };
      }

      let sqlQuery;
      let countQuery;
      const params = {};
      const whereClause = [];

      // Custom queries for specific table names
      if (tableName === "str_district") {
        sqlQuery = `
          SELECT 
            sd.districtName,
            sd.employeeId,
            ee.firstName + ' ' + ee.lastName as 'employeeName',
            sd.createDate,
            sd.validFrom,
            sd.validTo,
            sd.districtid
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_district sd
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sd.employeeId = ee.employeeId`;

        countQuery = `
          SELECT COUNT(*) AS totalCount 
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_district sd
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sd.employeeId = ee.employeeId`;
      } else if (tableName === "str_region") {
        sqlQuery = `
          SELECT 
            sr.regionName,
            sr.employeeId,
            ee.firstName + ' ' + ee.lastName as 'employeeName',
            sr.createDate,
            sr.validFrom,
            sr.validTo,
            sr.regionid
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_region sr
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sr.employeeId = ee.employeeId`;

        countQuery = `
          SELECT COUNT(*) AS totalCount 
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_region sr
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sr.employeeId = ee.employeeId`;
      } else if (tableName === "str_regionDistrict") {
        sqlQuery = `
          SELECT 
            srd.regionDistrictId,
            srd.regionId,
            srd.districtId,
            sr.employeeId as regionEmpId,
            sd.employeeId as districtEmpId,
            ee.firstName + ' ' + ee.lastName as 'regionName',
            ee2.firstName + ' ' + ee2.lastName as 'districtName',
            srd.createDate,
            srd.validFrom,
            srd.validTo
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_regionDistrict srd
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.str_region sr ON srd.regionId = sr.regionid
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.str_district sd ON srd.districtId = sd.districtid
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sr.employeeId = ee.employeeId
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee2 ON sd.employeeId = ee2.employeeId`;

        countQuery = `
          SELECT COUNT(*) AS totalCount 
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.str_regionDistrict srd
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.str_region sr ON srd.regionId = sr.regionid
          JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.str_district sd ON srd.districtId = sd.districtid
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee ON sr.employeeId = ee.employeeId
          LEFT JOIN ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee ee2 ON sd.employeeId = ee2.employeeId`;
      } else {
        // Default query for other tables
        sqlQuery = `SELECT * FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName}`;
        countQuery = `SELECT COUNT(*) AS totalCount FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName}`;
      }

      if (searchColumns && searchText && Array.isArray(searchColumns) && searchColumns.length > 0) {
        searchColumns.forEach((column, index) => {
          whereClause.push(`${column} LIKE @searchText${index}`);
          params[`searchText${index}`] = `%${searchText}%`;
        });

        if (whereClause.length > 0) {
          const whereStatement = `(${whereClause.join(" OR ")})`;
          sqlQuery += ` WHERE ${whereStatement}`;
          countQuery += ` WHERE ${whereStatement}`;
        }
      } else {
        context.log("No valid search conditions provided");
      }

      const offset = (page - 1) * limit;
      sqlQuery += ` ORDER BY (SELECT NULL) OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

      context.log("Final sqlQuery:", sqlQuery);

      const countResult = await executeQueryStoreLogisticsManager(countQuery, params);
      const totalCount = countResult[0].totalCount;

      const result = await executeQueryStoreLogisticsManager(sqlQuery, params);

      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName,
          limit,
          currentPage: page,
          totalPages,
          totalCount,
          count: result.length,
          result,
        },
      };
    } catch (error) {
      context.log("Error in getTableView:", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

app.http("getTableData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-table-data",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
      const body = await request.text();
      const { tableName, id } = body ? JSON.parse(body) : {};
      const alias = tableName + ".*";

      // console.log("tableName", tableName);
      // console.log("id", id);

      if (tableName === undefined || id === undefined) {
        return {
          status: 400,
          jsonBody: { error: "tableName and id are required" },
        };
      }

      if (!allowedTables.includes(tableName)) {
        return {
          status: 400,
          jsonBody: {
            error: `Invalid table name: ${tableName}`,
            allowedTables: allowedTables.join(", "),
          },
        };
      }

      sqlQuery = `
    SELECT ${alias}
    FROM str_store 
    LEFT JOIN str_site ON str_store.siteId = str_site.siteId
    LEFT JOIN str_storeConcept ON str_storeConcept.storeConceptId = str_store.storeConceptId
    LEFT JOIN str_market ON str_market.marketId = str_store.marketId
    LEFT JOIN str_district ON str_store.districtid = str_district.districtid
    LEFT JOIN str_regionDistrict ON str_district.districtid = str_regionDistrict.districtid
    LEFT JOIN str_region ON str_region.regionId = str_regionDistrict.regionId
    LEFT JOIN emp_employee AS district_employee ON district_employee.employeeId = str_district.employeeId
    LEFT JOIN emp_employee AS region_employee ON region_employee.employeeId = str_region.employeeId
    LEFT JOIN str_geography ON str_geography.geographyId = str_store.geographyId
    WHERE str_store.storeid = ${id}`;

      // console.log(sqlQuery);
      const result = await executeQueryStoreLogisticsManager(sqlQuery);
      return {
        status: 200,
        jsonBody: { result, sqlQuery },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error, sqlQuery },
      };
    }
  },
});

// search api
app.http("searchStores", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "stores/search",
  handler: async (request, context) => {
    await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
    let sqlQuery = null;
    try {
      const body = await request.text();
      const { searchText } = body ? JSON.parse(body) : {};

      if (!searchText) {
        throw new Error("Search text is required");
      }

      const fieldsToSearch = [
        "[SQL ID]",
        "[storeName]",
        "[Store Number]",
        "State",
        "[Store Code]",
        "[Legacy Store ID]",
        "[Store]",
        "[Phone]",
        "[siteAddress1]",
        "[siteAddress2]",
        "[city]",
        "[stateAbbreviation]",
        "[zip]",
        "[storeConceptName]",
        "[Open Year]",
        "[openDate]",
        "[marketName]",
        "[geographyName]",
        "[Region]",
        "[District]",
      ];
      const conditions = fieldsToSearch.map((field) => `${field} LIKE '%${searchText}%'`).join(" OR ");

      sqlQuery = `SELECT * FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.storeLogisticsManagerTableName} WHERE ${conditions}`;

      context.log("sqlQuery", sqlQuery);
      const searchResults = await executeQueryStoreLogisticsManager(sqlQuery);

      return {
        status: 200,
        jsonBody: { count: searchResults.length, sqlQuery, results: searchResults },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  },
});

// Config API
app.http("getConfig", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "config",
  handler: async (request, context) => {
    await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
    try {
      const configFields = ["geographyName", "Region", "District", "marketName", "storeConceptName", "storeName"];
      const query = `
        SELECT ${configFields.join(", ")}
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.storeLogisticsManagerTableName}
        WHERE ${configFields.map((field) => `${field} IS NOT NULL AND ${field} != ''`).join(" OR ")}
      `;

      context.log("Query:", query);
      const result = await executeQueryStoreLogisticsManager(query);
      context.log("Raw result:", JSON.stringify(result));

      const config = {};
      for (const field of configFields) {
        config[field] = [...new Set(result.map((row) => row[field]).filter(Boolean))].sort((a, b) => a.localeCompare(b));
      }

      context.log("Final config:", JSON.stringify(config));
      return {
        status: 200,
        jsonBody: { config },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

app.http("updateStores", {
  methods: ["PUT"],
  authLevel: "anonymous",
  route: "stores",
  handler: withLogging(async (request, context, setLoggingData) => {
    const queries = [];
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.STORE_LOGISTIC_ADMIN]);

      const body = await request.json();
      const { data, storeId } = body;

      if (!data || !storeId) {
        throw new Error("Invalid request body. 'data' and 'storeId' are required.");
      }

      const joinClause = `
        FROM ${CONFIG.schema}.str_store s
        LEFT JOIN ${CONFIG.schema}.str_site si ON s.siteId = si.siteId
        LEFT JOIN ${CONFIG.schema}.str_storeConcept sc ON sc.storeConceptId = s.storeConceptId
        LEFT JOIN ${CONFIG.schema}.str_market m ON m.marketId = s.marketId
        LEFT JOIN ${CONFIG.schema}.str_district d ON s.districtid = d.districtid
        LEFT JOIN ${CONFIG.schema}.str_regionDistrict rd ON d.districtid = rd.districtid
        LEFT JOIN ${CONFIG.schema}.str_region r ON r.regionId = rd.regionId
        LEFT JOIN ${CONFIG.schema}.emp_employee e ON e.employeeId = d.employeeId
        LEFT JOIN ${CONFIG.schema}.emp_employee ed ON ed.employeeId = r.employeeId
        LEFT JOIN ${CONFIG.schema}.str_geography g ON g.geographyId = s.geographyId
        WHERE s.storeId = @storeId
      `;

      for (const [tableName, tableData] of Object.entries(data)) {
        const updateFields = Object.entries(tableData)
          .map(([field]) => `${field} = @${field}`)
          .join(", ");

        if (updateFields) {
          let tableAlias;
          switch (tableName) {
            case "str_store":
              tableAlias = "s";
              break;
            case "str_site":
              tableAlias = "si";
              break;
            case "str_storeConcept":
              tableAlias = "sc";
              break;
            case "str_market":
              tableAlias = "m";
              break;
            case "str_district":
              tableAlias = "d";
              break;
            case "str_regionDistrict":
              tableAlias = "rd";
              break;
            case "str_region":
              tableAlias = "r";
              break;
            case "district_employee":
              tableAlias = "e";
              break;
            case "region_employee":
              tableAlias = "ed";
              break;
            case "str_geography":
              tableAlias = "g";
              break;
            default:
              throw new Error(`Unknown table: ${tableName}`);
          }

          const query = `
            UPDATE ${tableAlias}
            SET ${updateFields}
            ${joinClause}
          `;

          queries.push({
            query,
            params: { ...tableData, storeId: Number(storeId) },
          });
        }
      }

      if (queries.length === 0) {
        throw new Error("No valid fields to update");
      }

      const results = await executeTransaction(queries);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, queries);

      return {
        status: 200,
        jsonBody: { message: "Store updated successfully", results, queries },
      };
    } catch (error) {
      context.log("error", error);
      context.log("Error updating store:", error.message);
      return {
        status: 500,
        jsonBody: { error: error.message, queries },
      };
    }
  }),
});

app.http("insertStore", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "stores",
  handler: withLogging(async (request, context, setLoggingData) => {
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.STORE_LOGISTIC_ADMIN]);
      const body = await request.json();
      const { data } = body;

      if (!data || Object.keys(data).length === 0) {
        throw new Error("Invalid request body. 'data' is required and must contain at least one table.");
      }

      const currentDate = moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss");

      const tableQueries = {
        str_store: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_store (storeId, companyId, siteId, posId, loyaltyId, storeConceptId, dataProviderId,
            storeNumber, storeName, openDate, closedDate, showReporting, compareDate, isClosed, createDate, validFrom,
            validTo, marketId, legacyStoreCode, legacyStoreId, storePhone, geographyId, isComp, upcomingCompDate, districtId, cohortId)
            VALUES (@storeId, @companyId, @siteId, @posId, @loyaltyId, @storeConceptId, @dataProviderId, @storeNumber,
            @storeName, @openDate, @closedDate, @showReporting, @compareDate, @isClosed, @createDate, @validFrom, @validTo,
            @marketId, @legacyStoreCode, @legacyStoreId, @storePhone, @geographyId, @isComp, @upcomingCompDate, @districtId, @cohortId)
          `,
          requiredFields: [
            "storeId",
            "companyId",
            "siteId",
            "posId",
            "loyaltyId",
            "storeConceptId",
            "dataProviderId",
            "storeNumber",
            "storeName",
            "openDate",
            "closedDate",
            "showReporting",
            "compareDate",
            "isClosed",
            "validFrom",
            "validTo",
            "marketId",
            "legacyStoreCode",
            "legacyStoreId",
            "storePhone",
            "geographyId",
            "isComp",
            "upcomingCompDate",
            "districtId",
            "cohortId",
          ],
        },
        str_site: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_site (siteId, siteAddress1, siteAddress2, city, state, stateAbbreviation, zip, squareFootage,
            latitude, longitude, geographyPoint, createDate, validFrom, validTo, siteName, projectedOpenDate)
            VALUES (@siteId, @siteAddress1, @siteAddress2, @city, @state, @stateAbbreviation, @zip, @squareFootage, @latitude,
            @longitude, geography::STPointFromText(@geographyPoint, 4326), @createDate, @validFrom, @validTo, @siteName, @projectedOpenDate)
          `,
          requiredFields: [
            "siteId",
            "siteAddress1",
            "siteAddress2",
            "city",
            "state",
            "stateAbbreviation",
            "zip",
            "squareFootage",
            "latitude",
            "longitude",
            "geographyPoint",
            "validFrom",
            "validTo",
            "siteName",
            "projectedOpenDate",
          ],
        },
        str_storeConcept: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_storeConcept (storeConceptId, storeConceptName, createDate, validFrom, validTo)
            VALUES (@storeConceptId, @storeConceptName, @createDate, @validFrom, @validTo)
          `,
          requiredFields: ["storeConceptId", "storeConceptName", "validFrom", "validTo"],
        },
        str_market: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_market (marketId, marketName, createDate, validFrom, validTo)
            VALUES (@marketId, @marketName, @createDate, @validFrom, @validTo)
          `,
          requiredFields: ["marketId", "marketName", "validFrom", "validTo"],
        },
        str_district: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_district (districtName, employeeId, createDate, validFrom, validTo, districtid)
            VALUES (@districtName, @employeeId, @createDate, @validFrom, @validTo, @districtid)
          `,
          requiredFields: ["districtName", "employeeId", "validFrom", "validTo", "districtid"],
        },
        str_regionDistrict: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_regionDistrict (regionDistrictId, regionId, districtId, createDate, validFrom, validTo)
            VALUES (@regionDistrictId, @regionId, @districtId, @createDate, @validFrom, @validTo)
          `,
          requiredFields: ["regionDistrictId", "regionId", "districtId", "validFrom", "validTo"],
        },
        str_region: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_region (regionName, employeeId, createDate, validFrom, validTo, regionid)
            VALUES (@regionName, @employeeId, @createDate, @validFrom, @validTo, @regionid)
          `,
          requiredFields: ["regionName", "employeeId", "validFrom", "validTo", "regionid"],
        },
        str_geography: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.str_geography (geographyId, geographyName, createDate, validFrom, validTo)
            VALUES (@geographyId, @geographyName, @createDate, @validFrom, @validTo)
          `,
          requiredFields: ["geographyId", "geographyName", "validFrom", "validTo"],
        },
        emp_employee: {
          query: `
            INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.emp_employee (employeeId, companyId, employeeNumber, firstName, lastName, email, 
            departmentId, hireDate, jobId, termDate, isActive, managerEmployeeNumber, createDate, validFrom, validTo, phone)
            VALUES (@employeeId, @companyId, @employeeNumber, @firstName, @lastName, @email, @departmentId, @hireDate, 
            @jobId, @termDate, @isActive, @managerEmployeeNumber, @createDate, @validFrom, @validTo, @phone)
          `,
          requiredFields: [
            "employeeId",
            "companyId",
            "employeeNumber",
            "firstName",
            "lastName",
            "email",
            "departmentId",
            "hireDate",
            "jobId",
            "termDate",
            "isActive",
            "managerEmployeeNumber",
            "validFrom",
            "validTo",
            "phone",
          ],
        },
      };

      const queries = [];

      for (const [tableName, tableData] of Object.entries(data)) {
        if (!tableQueries[tableName]) {
          throw new Error(`Invalid table name: ${tableName}`);
        }

        const { query, requiredFields } = tableQueries[tableName];
        const missingFields = requiredFields.filter((field) => !(field in tableData));

        if (missingFields.length > 0) {
          throw new Error(`Missing required fields for ${tableName}: ${missingFields.join(", ")}`);
        }

        queries.push({
          query,
          params: { ...tableData, createDate: currentDate },
        });
      }

      if (queries.length === 0) {
        throw new Error("No valid tables to insert");
      }

      const results = await executeTransaction(queries);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, queries);

      return {
        status: 200,
        jsonBody: { message: "Store data inserted successfully", results },
      };
    } catch (error) {
      context.log("Error inserting store data:", error.message);
      return {
        status: 400,
        jsonBody: { error: error.message },
      };
    }
  }),
});

// Check if value exists in the specified table
//sample body:
// {
//   "tableName": "str_store",
//   "columnName": "storeName",
//   "value": "New Store Name"
//   "isEDW": false
// }
// sample response:
// {
//   "query": "SELECT COUNT(*) AS count FROM ems.str_store WHERE storeName = @value",
//   "exists": true,
//   "tableName": "str_store",
//   "columnName": "storeName",
//   "value": "New Store Name"
// }
app.http("checkValueExists", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "check-value-exists",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER, ROLES.STORE_LOGISTIC_ADMIN, ROLES.STORE_LOGISTIC_READER]);
      const body = await request.json();
      const { tableName, schemaName = `${CONFIG.schema}`, columnName, value, isEDW = false } = body;

      // Validate required fields
      if (!tableName || !columnName || value === undefined) {
        return {
          status: 400,
          jsonBody: { error: "tableName, columnName, and value are required" },
        };
      }

      const sanitizedColumnName = columnName.replace(/[^a-zA-Z0-9_]/g, "");
      const params = { value };

      // Prepare SQL query based on whether it's EDW or not
      const sqlQuery = isEDW
        ? `
          SELECT COUNT(*) AS count
          FROM ${CONFIG.dbName}.${schemaName}.${tableName}
          WHERE ${sanitizedColumnName} = @value
        `
        : `
          SELECT COUNT(*) AS count
          FROM ${CONFIG.dbNameEms}.${schemaName}.${tableName}
          WHERE ${sanitizedColumnName} = @value
        `;

      context.log("SQL Query:", sqlQuery);
      context.log("Params:", params);

      // Execute the query
      const result = isEDW ? await executeQuery(sqlQuery, params) : await executeQueryStoreLogisticsManager(sqlQuery, params);

      const exists = result[0].count > 0;

      return {
        status: 200,
        jsonBody: {
          query: sqlQuery,
          exists,
          tableName,
          columnName: sanitizedColumnName,
          value,
        },
      };
    } catch (error) {
      context.log("Error in checkValueExists:", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  },
});

// Update single table data
//sample body:
// {
//   "tableName": "str_store",
//   "data": { "storeName": "New Store Name" },
//   "primaryKeyColumn": "storeId",
//   "primaryKeyValue": 123
// }

app.http("updateTableView", {
  methods: ["PUT"],
  authLevel: "anonymous",
  route: "update-table-view",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;

    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.STORE_LOGISTIC_ADMIN]);
      const body = await request.json();
      const { tableName, data, primaryKeyColumn, primaryKeyValue } = body;

      // Delete createDate if it exists
      if (data.createDate) {
        delete data.createDate;
      }

      // Validate required fields
      if (!tableName || !data || !primaryKeyColumn || !primaryKeyValue) {
        return {
          status: 400,
          jsonBody: { error: "tableName, data, primaryKeyColumn, and primaryKeyValue are required" },
        };
      }

      // Verify table is allowed
      if (!queryTables.includes(tableName)) {
        return {
          status: 400,
          jsonBody: {
            error: `Invalid table name: ${tableName}`,
            queryTables: queryTables.join(", "),
          },
        };
      }

      // Validate that primaryKeyColumn is actually a primary key for this table
      if (PRIMARY_KEYS[tableName] && !PRIMARY_KEYS[tableName].includes(primaryKeyColumn)) {
        return {
          status: 400,
          jsonBody: {
            error: `Invalid primary key column. Valid primary keys for ${tableName} are: ${PRIMARY_KEYS[tableName].join(", ")}`,
          },
        };
      }

      // Validate that primary keys are not being modified
      if (PRIMARY_KEYS[tableName]) {
        const attemptedPrimaryKeyChanges = PRIMARY_KEYS[tableName].filter(
          (key) => key in data && data[key] !== primaryKeyValue && key === primaryKeyColumn
        );

        if (attemptedPrimaryKeyChanges.length > 0) {
          return {
            status: 400,
            jsonBody: {
              error: `Cannot modify primary key fields: ${attemptedPrimaryKeyChanges.join(", ")}`,
            },
          };
        }
      }

      // Check if the primary key exists
      const checkQuery = `
        SELECT COUNT(*) AS count 
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName} 
        WHERE ${primaryKeyColumn} = @primaryKeyValue
      `;

      const checkResult = await executeQueryStoreLogisticsManager(checkQuery, { primaryKeyValue });

      if (checkResult[0].count === 0) {
        return {
          status: 404,
          jsonBody: { error: `Record with ${primaryKeyColumn} = ${primaryKeyValue} not found` },
        };
      }

      // Get current data for the record
      const getCurrentDataQuery = `
        SELECT * 
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName} 
        WHERE ${primaryKeyColumn} = @primaryKeyValue
      `;

      const currentDataResult = await executeQueryStoreLogisticsManager(getCurrentDataQuery, { primaryKeyValue });
      const currentData = currentDataResult[0];

      // Remove any primary key fields from data to prevent modification
      if (PRIMARY_KEYS[tableName]) {
        PRIMARY_KEYS[tableName].forEach((key) => {
          if (key in data) {
            context.log(`Removing primary key field ${key} from update data`);
            delete data[key];
          }
        });
      }

      // Merge current data with new data for validation
      const mergedData = { ...currentData, ...data };

      // Validate required fields
      if (NOT_NULL[tableName]) {
        const missingRequiredFields = NOT_NULL[tableName].filter(
          (field) => mergedData[field] === undefined || mergedData[field] === null || mergedData[field] === ""
        );

        if (missingRequiredFields.length > 0) {
          return {
            status: 400,
            jsonBody: {
              error: `The following required fields cannot be null or empty: ${missingRequiredFields.join(", ")}`,
              missingFields: missingRequiredFields,
            },
          };
        }
      }

      // Build UPDATE query
      const updateFields = [];
      const params = { primaryKeyValue };

      Object.entries(data).forEach(([field, value]) => {
        // Skip updating the primary key
        if (field !== primaryKeyColumn) {
          updateFields.push(`${field} = @${field}`);
          params[field] = value;
        }
      });

      if (updateFields.length === 0) {
        return {
          status: 400,
          jsonBody: { error: "No fields to update" },
        };
      }

      // Set createDate if table has it
      // const currentDate = new Date().toISOString();
      // if ("createDate" in currentData && !data.createDate) {
      //   updateFields.push("createDate = @createDate");
      //   params.createDate = currentDate;
      // }

      sqlQuery = `
        UPDATE ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName}
        SET ${updateFields.join(", ")}
        WHERE ${primaryKeyColumn} = @primaryKeyValue
      `;

      context.log("Update SQL Query:", sqlQuery);
      context.log("Params:", params);

      // Execute the update query
      await executeQueryStoreLogisticsManager(sqlQuery, params);

      // Get the updated record
      const getUpdatedQuery = `
        SELECT * 
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName} 
        WHERE ${primaryKeyColumn} = @primaryKeyValue
      `;

      const updatedResult = await executeQueryStoreLogisticsManager(getUpdatedQuery, { primaryKeyValue });

      // Set logging data
      setLoggingData(tokenResult, {
        query: sqlQuery,
        params,
        action: "UPDATE",
        tableName,
        primaryKey: { column: primaryKeyColumn, value: primaryKeyValue },
      });

      return {
        status: 200,
        jsonBody: {
          message: "Record updated successfully",
          tableName,
          primaryKey: { column: primaryKeyColumn, value: primaryKeyValue },
          updatedFields: Object.keys(data),
          result: updatedResult[0],
        },
      };
    } catch (error) {
      context.log("Error in updateTableView:", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});

// Insert single table data
// Sample body:
// {
//   "tableName": "str_store",
//   "data": {
//     "storeId": 456,
//     "companyId": 1,
//     "siteId": 100,
//     "storeNumber": "S123",
//     "storeName": "New Store"
// ... other required fields
//   }
// }
app.http("insertTableView", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "insert-table-view",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;

    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.STORE_LOGISTIC_ADMIN]);
      const body = await request.json();
      const { tableName, data } = body;

      // Delete createDate if it exists
      if (data.createDate) {
        delete data.createDate;
      }

      // Validate required fields
      if (!tableName || !data) {
        return {
          status: 400,
          jsonBody: { error: "tableName and data are required" },
        };
      }

      // Verify table is allowed
      if (!queryTables.includes(tableName)) {
        return {
          status: 400,
          jsonBody: {
            error: `Invalid table name: ${tableName}`,
            queryTables: queryTables.join(", "),
          },
        };
      }

      // Validate required fields
      if (NOT_NULL[tableName]) {
        const missingRequiredFields = NOT_NULL[tableName].filter((field) => data[field] === undefined || data[field] === null || data[field] === "");

        if (missingRequiredFields.length > 0) {
          return {
            status: 400,
            jsonBody: {
              error: `The following required fields cannot be null or empty: ${missingRequiredFields.join(", ")}`,
              missingFields: missingRequiredFields,
            },
          };
        }
      }

      // Validate that identity keys are not provided
      if (IDENTITY_KEYS[tableName]) {
        const providedIdentityKeys = IDENTITY_KEYS[tableName].filter((key) => data[key] !== undefined && data[key] !== null && data[key] !== "");

        if (providedIdentityKeys.length > 0) {
          return {
            status: 400,
            jsonBody: {
              error: `The following identity keys must not be provided: ${providedIdentityKeys.join(", ")}`,
              providedKeys: providedIdentityKeys,
            },
          };
        }
      }

      // Check if record with the same primary key already exists
      if (PRIMARY_KEYS[tableName]) {
        const keyConditions = PRIMARY_KEYS[tableName]
          .map((key) => {
            if (data[key] === undefined) {
              return null;
            }
            return `${key} = @${key}`;
          })
          .filter(Boolean);

        if (keyConditions.length > 0) {
          const checkQuery = `
            SELECT COUNT(*) AS count 
            FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName} 
            WHERE ${keyConditions.join(" AND ")}
          `;

          const params = {};
          PRIMARY_KEYS[tableName].forEach((key) => {
            if (data[key] !== undefined) {
              params[key] = data[key];
            }
          });

          const checkResult = await executeQueryStoreLogisticsManager(checkQuery, params);

          if (checkResult[0].count > 0) {
            return {
              status: 409,
              jsonBody: { error: `Record with the same primary key already exists in ${tableName}` },
            };
          }
        }
      }

      // Add current date to data if the table has createDate field
      // const currentDate = new Date().toISOString();
      // const dataWithDates = { ...data };
      // if (!dataWithDates.createDate) {
      //   dataWithDates.createDate = currentDate;
      // }
      // if (!dataWithDates.validFrom) {
      //   dataWithDates.validFrom = currentDate;
      // }
      // if (!dataWithDates.validTo && tableName !== "str_site") {
      //   dataWithDates.validTo = "9999-12-31T23:59:59.999Z";
      // }

      // Build INSERT query
      const fields = Object.keys(data);
      const placeholders = fields.map((field) => `@${field}`);

      sqlQuery = `
        INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName}
        (${fields.join(", ")})
        VALUES (${placeholders.join(", ")})
      `;

      context.log("Insert SQL Query:", sqlQuery);
      context.log("Params:", data);

      // Execute the insert query
      await executeQueryStoreLogisticsManager(sqlQuery, data);

      // Get the inserted record
      let getInsertedQuery = null;
      if (PRIMARY_KEYS[tableName] && PRIMARY_KEYS[tableName].every((key) => data[key] !== undefined)) {
        const conditions = PRIMARY_KEYS[tableName].map((key) => `${key} = @${key}`).join(" AND ");

        getInsertedQuery = `
          SELECT * 
          FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${tableName} 
          WHERE ${conditions}
        `;

        const params = {};
        PRIMARY_KEYS[tableName].forEach((key) => {
          params[key] = data[key];
        });

        const insertedResult = await executeQueryStoreLogisticsManager(getInsertedQuery, params);

        // Set logging data
        setLoggingData(tokenResult, {
          query: sqlQuery,
          params: data,
          action: "INSERT",
          tableName,
        });

        return {
          status: 201,
          jsonBody: {
            message: "Record inserted successfully",
            tableName,
            insertedFields: Object.keys(data),
            result: insertedResult[0] || null,
          },
        };
      }

      // Set logging data
      setLoggingData(tokenResult, {
        query: sqlQuery,
        params: data,
        action: "INSERT",
        tableName,
      });

      return {
        status: 201,
        jsonBody: {
          message: "Record inserted successfully",
          tableName,
          insertedFields: Object.keys(data),
        },
      };
    } catch (error) {
      context.log("Error in insertTableView:", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});
