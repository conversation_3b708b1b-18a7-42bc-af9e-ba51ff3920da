const { app } = require("@azure/functions");
const { DefaultAzureCredential } = require("@azure/identity");
const { SecretClient } = require("@azure/keyvault-secrets");
const CONFIG = require("./config");
const { executeQueryStoreLogisticsManager } = require("./helper/helper");
const { authenticate } = require("./helper/auth");
const moment = require("moment-timezone");
const xlsx = require("xlsx");
const { ROLES } = require("./helper/constants");
const { withLogging } = require("./helper/loggingMiddleware");

const credential = new DefaultAzureCredential();
const vaultName = process.env["KEY_VAULT_NAME"];
const url = `https://${vaultName}.vault.azure.net`;
const client = new SecretClient(url, credential);

// get all stores list from SQL DB with pagination
app.http("getFiscalCalendar", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "get-fiscal-calendar",
  handler: async (request, context) => {
    let sqlQuery = null;
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const body = await request.text();

      const { page = 1, limit = 200, filters = {} } = body ? JSON.parse(body) : {};
      const offset = (page - 1) * limit;

      let whereClause = "";
      const filterFields = [
        "fiscalCalendarId",
        "calendarDate",
        "previousYearDate",
        "dayName",
        "dayOfWeek",
        "fiscalPeriodNumber",
        "fiscalWeekNumber",
        "fiscalQuarterNumber",
        "fiscalYear",
        "createDate",
        "isPayPeriodEnd",
        "isPayPeriodStart",
        "Holiday",
      ];

      const filterConditions = filterFields
        .filter((field) => filters[field])
        .map((field) => {
          let values = filters[field];
          if (typeof values === "string") {
            values = values.split(",").map((v) => v.trim());
          } else if (!Array.isArray(values)) {
            values = [values];
          }
          return `${field} IN (${values.map((v) => `'${v}'`).join(", ")})`;
        });

      // Handle fiscal year filtering
      if (filters.fiscalYear) {
        filterConditions.push(`fiscalYear = '${filters.fiscalYear}'`);
      } else {
        // If no fiscal year provided, get the latest fiscal year
        const latestYear = new Date().getFullYear();
        filterConditions.push(`fiscalYear = '${latestYear}'`);
      }

      if (filterConditions.length > 0) {
        whereClause = `WHERE ${filterConditions.join(" AND ")}`;
      }

      const totalCountQuery = `SELECT COUNT(*) AS count FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarViewName} ${whereClause}`;
      const totalCountResult = await executeQueryStoreLogisticsManager(totalCountQuery);
      const totalCount = totalCountResult[0].count;
      const totalPages = Math.ceil(totalCount / limit);

      sqlQuery = `
        SELECT * FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarViewName}
        ${whereClause}
      ORDER BY
      CASE
      WHEN ISNUMERIC([fiscalCalendarId]) = 1 THEN CAST([fiscalCalendarId] AS INT)
      ELSE NULL
      END ASC,
      [fiscalCalendarId] ASC
      OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

      context.log("sqlQuery", sqlQuery);
      const result = await executeQueryStoreLogisticsManager(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          sqlQuery,
          tableName: CONFIG.fiscalCalendarViewName,
          limit,
          currentPage: page,
          totalPages,
          totalCount,
          count: result.length,
          result,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error, sqlQuery },
      };
    }
  },
});

// search api
app.http("searchFiscalCalendar", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "fiscal-calendar/search",
  handler: async (request, context) => {
    await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
    let sqlQuery = null;
    try {
      const body = await request.text();
      const { searchText } = body ? JSON.parse(body) : {};

      if (!searchText) {
        throw new Error("Search text is required");
      }

      const fieldsToSearch = [
        "fiscalCalendarId",
        "calendarDate",
        "previousYearDate",
        "dayName",
        "dayOfWeek",
        "fiscalPeriodNumber",
        "fiscalWeekNumber",
        "fiscalQuarterNumber",
        "fiscalYear",
        "createDate",
        "isPayPeriodEnd",
        "isPayPeriodStart",
      ];

      const conditions = fieldsToSearch.map((field) => `${field} LIKE '%${searchText}%'`).join(" OR ");

      sqlQuery = `SELECT * FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarViewName} WHERE ${conditions}`;

      context.log("sqlQuery", sqlQuery);
      const searchResults = await executeQueryStoreLogisticsManager(sqlQuery);

      return {
        status: 200,
        jsonBody: { count: searchResults.length, sqlQuery, results: searchResults },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  },
});

// Config API
app.http("getConfigForFiscalCalendar", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "fiscal-calendar/config",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);

      const configFields = ["fiscalYear", "fiscalPeriodNumber", "fiscalWeekNumber", "fiscalQuarterNumber"];
      const query = `
        SELECT DISTINCT ${configFields.join(", ")}
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarViewName}
        WHERE ${configFields.map((field) => `${field} IS NOT NULL AND ${field} != ''`).join(" AND ")}
        ORDER BY ${configFields.join(", ")} DESC
      `;

      context.log("Query:", query);
      const result = await executeQueryStoreLogisticsManager(query);

      const config = configFields.reduce((acc, field) => {
        acc[field] = [...new Set(result.map((row) => row[field]))];
        return acc;
      }, {});

      return {
        status: 200,
        jsonBody: { config },
      };
    } catch (error) {
      context.log("Error in getConfigForFiscalCalendar:", error);
      return {
        status: 500,
        jsonBody: { error: "An error occurred while fetching the configuration.", message: error.message },
      };
    }
  },
});

// update fiscal calendar
app.http("updateFiscalCalendar", {
  methods: ["PUT"],
  authLevel: "anonymous",
  route: "update-fiscal-calendar",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER]);
      const body = await request.text();
      const { fiscalCalendarId, filters } = body ? JSON.parse(body) : {};

      if (!fiscalCalendarId) {
        throw new Error("fiscalCalendarId is required");
      }

      if (!Array.isArray(filters) || filters.length === 0) {
        throw new Error("filters must be a non-empty array");
      }

      const allowedFields = [
        "dayName",
        "dayOfWeek",
        "fiscalPeriodNumber",
        "fiscalWeekNumber",
        "fiscalQuarterNumber",
        "fiscalYear",
        "createDate",
        "isPayPeriodEnd",
        "isPayPeriodStart",
        "Holiday",
        "FutureFiscalYear",
        "FutureFiscalPeriodNumber",
      ];
      const updateFields = [];
      const params = { fiscalCalendarId };

      filters.forEach((filter) => {
        const { field, value } = filter;
        if (!allowedFields.includes(field)) {
          throw new Error(`Field '${field}' is not allowed for update`);
        }
        updateFields.push(`${field} = @${field}`);
        params[field] = value;
      });

      if (updateFields.length === 0) {
        throw new Error("No valid fields to update");
      }

      sqlQuery = `
        UPDATE ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarTableName}
        SET ${updateFields.join(", ")}
        WHERE fiscalCalendarId = @fiscalCalendarId
      `;

      context.log("sqlQuery", sqlQuery);
      context.log("params", params);

      const result = await executeQueryStoreLogisticsManager(sqlQuery, params);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, sqlQuery);

      if (result.rowsAffected && result.rowsAffected[0] === 0) {
        return {
          status: 404,
          jsonBody: { error: "No fiscal calendar entry found with the given ID" },
        };
      }

      return {
        status: 200,
        jsonBody: {
          message: "Fiscal calendar entry updated successfully",
          fiscalCalendarId,
          updatedFields: Object.keys(params).filter((key) => key !== "fiscalCalendarId"),
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});

// add fiscal calendar
app.http("addFiscalCalendar", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "add-fiscal-calendar",
  handler: withLogging(async (request, context, setLoggingData) => {
    let sqlQuery = null;
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER]);
      const body = await request.text();
      const newEntry = body ? JSON.parse(body) : {};

      const requiredFields = [
        "fiscalCalendarId",
        "calendarDate",
        "previousYearDate",
        "dayName",
        "dayOfWeek",
        "fiscalPeriodNumber",
        "fiscalWeekNumber",
        "fiscalQuarterNumber",
        "fiscalYear",
        "createDate",
        "isPayPeriodEnd",
        "isPayPeriodStart",
        "Holiday",
      ];

      const missingFields = requiredFields.filter((field) => !(field in newEntry));
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
      }

      const checkDuplicateQuery = `
        SELECT COUNT(*) AS count
        FROM ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarTableName}
        WHERE fiscalCalendarId = @fiscalCalendarId
           OR calendarDate = @calendarDate
           OR previousYearDate = @previousYearDate
      `;

      const duplicateCheckResult = await executeQueryStoreLogisticsManager(checkDuplicateQuery, {
        fiscalCalendarId: newEntry.fiscalCalendarId,
        calendarDate: newEntry.calendarDate,
        previousYearDate: newEntry.previousYearDate,
      });

      console.log("duplicateCheckResult", duplicateCheckResult);

      if (duplicateCheckResult[0].count > 0) {
        return {
          status: 409,
          jsonBody: {
            error: "Duplicate entry found for the provided fiscal calendar ID or calendarDate or previousYearDate.",
            details: {
              fiscalCalendarId: newEntry.fiscalCalendarId,
              calendarDate: newEntry.calendarDate,
              previousYearDate: newEntry.previousYearDate,
            },
          },
        };
      }

      const fields = requiredFields.join(", ");
      const values = requiredFields.map((field) => `@${field}`).join(", ");

      sqlQuery = `
        INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarTableName}
        (${fields})
        VALUES (${values})
      `;

      context.log("sqlQuery", sqlQuery);
      context.log("newEntry", newEntry);

      await executeQueryStoreLogisticsManager(sqlQuery, newEntry);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, sqlQuery);

      return {
        status: 201,
        jsonBody: {
          message: "Fiscal calendar entry added successfully",
          addedEntry: newEntry,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: error.message.includes("Duplicate entry") ? 409 : 500,
        jsonBody: { error: error.message, sqlQuery },
      };
    }
  }),
});

// bulk upload fiscal calendar
app.http("bulkUploadFiscalCalendar", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "bulk-upload-fiscal-calendar",
  handler: withLogging(async (request, context, setLoggingData) => {
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER]);

      const formData = await request.formData();
      const file = formData.get("file");

      if (!file) {
        throw new Error("No file uploaded");
      }

      const buffer = await file.arrayBuffer();
      const workbook = xlsx.read(buffer, { type: "buffer" });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = xlsx.utils.sheet_to_json(worksheet);

      const requiredFields = [
        "fiscalCalendarId",
        "calendarDate",
        "previousYearDate",
        "dayName",
        "dayOfWeek",
        "fiscalPeriodNumber",
        "fiscalWeekNumber",
        "fiscalQuarterNumber",
        "fiscalYear",
        "isPayPeriodEnd",
        "isPayPeriodStart",
        "Holiday",
      ];

      const missingFields = requiredFields.filter((field) => !(field in data[0]));
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields in Excel: ${missingFields.join(", ")}`);
      }

      const centralTime = moment().tz("America/Chicago").format("YYYY-MM-DD HH:mm:ss");

      const validatedData = [];
      const errors = [];
      const existingEntries = new Set();
      const existingCalendarDates = new Set();
      const existingPreviousYearDates = new Set();

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNum = i + 2;

        // Validate required fields, data types, and formats
        for (const field of requiredFields) {
          if (!row[field] && field !== "isPayPeriodEnd" && field !== "isPayPeriodStart" && field !== "Holiday") {
            errors.push(`Row ${rowNum}: Missing value for ${field}`);
          } else {
            switch (field) {
              case "fiscalCalendarId":
              case "dayOfWeek":
              case "fiscalPeriodNumber":
              case "fiscalWeekNumber":
              case "fiscalQuarterNumber":
              case "fiscalYear":
                if (!Number.isInteger(row[field])) {
                  errors.push(`Row ${rowNum}: ${field} must be an integer`);
                }
                break;
              case "calendarDate":
              case "previousYearDate":
                if (!moment(row[field], "YYYY-MM-DD", true).isValid()) {
                  errors.push(`Row ${rowNum}: ${field} must be a valid date in YYYY-MM-DD format`);
                }
                break;
              case "dayName": {
                const validDays = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
                if (typeof row[field] !== "string" || !validDays.includes(row[field].toLowerCase())) {
                  errors.push(`Row ${rowNum}: ${row[field]} must be a valid day name (e.g., Sunday, Monday, etc.)`);
                }
                break;
              }
              case "isPayPeriodEnd":
              case "isPayPeriodStart":
              case "Holiday":
                if (typeof row[field] !== "boolean") {
                  errors.push(`Row ${rowNum}: ${field} must be a boolean value`);
                }
                break;
            }
          }
        }

        // check for unique fiscalCalendarId
        if (existingEntries.has(row.fiscalCalendarId)) {
          errors.push(`Row ${rowNum}: Duplicate fiscalCalendarId ${row.fiscalCalendarId}`);
        } else {
          existingEntries.add(row.fiscalCalendarId);
        }

        // check for unique calendarDate
        if (existingCalendarDates.has(row.calendarDate)) {
          errors.push(`Row ${rowNum}: Duplicate calendar date ${row.calendarDate}`);
        } else {
          existingCalendarDates.add(row.calendarDate);
        }

        // check for unique previousYearDate
        if (existingPreviousYearDates.has(row.previousYearDate)) {
          errors.push(`Row ${rowNum}: Duplicate previous year date ${row.previousYearDate}`);
        } else {
          existingPreviousYearDates.add(row.previousYearDate);
        }

        // Set default values for boolean fields
        row.isPayPeriodEnd = row.isPayPeriodEnd === true;
        row.isPayPeriodStart = row.isPayPeriodStart === true;
        row.Holiday = row.Holiday === true;

        // Add createDate
        row.createDate = centralTime;

        validatedData.push(row);
      }

      if (errors.length > 0) {
        return {
          status: 400,
          jsonBody: { errors: errors },
        };
      }

      const fields = [...requiredFields, "createDate"];
      const values = validatedData.map((row) =>
        fields
          .map((field) => {
            if (field === "calendarDate" || field === "previousYearDate") {
              return `'${moment(row[field]).format("YYYY-MM-DD")}'`;
            }
            return typeof row[field] === "boolean" ? (row[field] ? 1 : 0) : `'${row[field]}'`;
          })
          .join(", ")
      );

      const sqlQuery = `
        INSERT INTO ${CONFIG.dbNameEms}.${CONFIG.schema}.${CONFIG.fiscalCalendarTableName}
        (${fields.join(", ")})
        VALUES
        ${values.map((v) => `(${v})`).join(",\n")}
      `;

      context.log("sqlQuery", sqlQuery);

      // Pass tokenResult and sqlQuery to logging middleware
      setLoggingData(tokenResult, sqlQuery);

      await executeQueryStoreLogisticsManager(sqlQuery);

      return {
        status: 200,
        jsonBody: {
          message: "Bulk upload successful",
          entriesAdded: validatedData.length,
        },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: error.message },
      };
    }
  }),
});

// API to check missing dates in fiscal calendar
app.http("checkMissingDates", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "fiscal-calendar/check-missing-dates",
  handler: async (request, context) => {
    try {
      await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const body = await request.text();
      const { startDate, endDate } = body ? JSON.parse(body) : {};

      if (!startDate || !endDate) {
        return {
          status: 400,
          jsonBody: { error: "startDate and endDate are required" },
        };
      }

      const query = `
      exec ${CONFIG.dbNameEms}.${CONFIG.schema}.USP_Missing_Dates @startDate='${moment(startDate).format("YYYY-MM-DD")}', @endDate='${moment(
        endDate
      ).format("YYYY-MM-DD")}'
      `;

      const result = await executeQueryStoreLogisticsManager(query);

      return {
        status: 200,
        jsonBody: { result },
      };
    } catch (error) {
      context.log("error", error);
      return {
        status: 500,
        jsonBody: { error: "An error occurred while checking missing dates.", details: error.message },
      };
    }
  },
});
