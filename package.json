{"name": "lou_store_details", "version": "1.0.0", "description": "", "scripts": {"start": "func start", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/cosmos": "^4.1.1", "@azure/functions": "^4.5.0", "@azure/keyvault-secrets": "^4.8.0", "@azure/synapse-artifacts": "^1.0.0-beta.15", "azure-ad-jwt": "^1.1.0", "moment-timezone": "^0.5.45", "tedious": "^18.3.0", "xlsx": "^0.18.5"}, "main": "src/{index.js,functions/*.js}", "devDependencies": {"@eslint/js": "^9.9.1", "eslint": "^9.9.1", "globals": "^15.9.0"}}