# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Development Commands

### Local Development
```bash
# Start the Azure Functions locally
npm start
# or
func start

# Run with specific configuration
func start --port 7071
```

### Linting
```bash
# Run ESLint on the codebase
npx eslint src/

# Run ESLint with auto-fix
npx eslint src/ --fix

# Check specific file
npx eslint src/functions/dashboard.js
```

### Testing
```bash
# Note: Tests not yet implemented
npm test
```

### Deployment
```bash
# Deploy to development environment
func azure functionapp publish lou-web-app-dev

# Deploy to production environment  
func azure functionapp publish lou-web-app-prod
```

### Dependencies
```bash
# Install all dependencies
npm install

# Install production dependencies only
npm install --production

# Update dependencies
npm update
```

## Architecture Overview

This is an Azure Functions Node.js application that serves as the API backend for the Lou Malnati's Web Application. It provides endpoints for store management, pipeline monitoring, and data operations.

### Core Structure

**Entry Points:**
- `src/index.js` - Main entry point (currently minimal)
- `src/functions/*.js` - Individual Azure Functions endpoints

**Function Modules:**
- `store-logistic-manager.js` - Store management operations (CRUD operations for stores, districts, regions)
- `dashboard.js` - Azure Synapse pipeline monitoring and management
- `report.js` - Reporting functionality
- `fiscalCalendar.js` - Fiscal calendar operations
- `promotions.js` - Promotion management
- `revel.js` - Revel POS integration
- `data-validation.js` - Data validation operations
- `audit-log.js` - Audit logging functionality

**Helper Modules:**
- `helper/auth.js` - JWT authentication using Azure AD
- `helper/helper.js` - Database query utilities
- `helper/constants.js` - Role definitions and allowed operations
- `helper/cosmos.js` - Cosmos DB operations
- `helper/loggingMiddleware.js` - Logging middleware

### Authentication & Authorization

The API uses Azure AD JWT tokens for authentication with role-based access control:

**Roles:**
- `Admin` - Full system access
- `Developer` - Development access
- `Reader` - Read-only access
- `Store-Logistic-Admin` - Store management admin
- `Store-Logistic-Reader` - Store management read-only
- `Discount-Page-Admin` - Discount management admin
- `Discount-Page-Reader` - Discount management read-only

### Database Architecture

**Primary Databases:**
- SQL Server for store/employee data (`DB_NAME`)
- EMS database (`DB_NAME_EMS`) for store logistics

**Key Tables:**
- `str_store` - Store information
- `str_site` - Physical site details  
- `str_district` - District organization
- `str_region` - Regional organization
- `emp_employee` - Employee data
- `str_storeConcept` - Store concepts/brands
- Fiscal calendar tables for time-based operations

### Environment Configuration

Required environment variables (stored in Azure Key Vault or local.settings.json):
- `DB_SERVER` - SQL Server instance
- `DB_USER`, `DB_PASSWORD` - Database credentials
- `DB_NAME`, `DB_NAME_EMS` - Database names
- `SCHEMA` - Database schema
- `AZURE_TENANT_ID`, `AZURE_CLIENT_ID`, `AZURE_CLIENT_SECRET` - Azure AD authentication
- `AZURE_SYNAPSE_ENDPOINT` - Synapse workspace endpoint
- `KEY_VAULT_NAME` - Azure Key Vault name

### API Patterns

**Common Request Pattern:**
```javascript
app.http("functionName", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "endpoint-name",
  handler: async (request, context) => {
    // 1. Authenticate and check roles
    await authenticate(request, context, [ROLES.ADMIN]);
    
    // 2. Parse request body
    const body = await request.json();
    
    // 3. Execute business logic
    const result = await executeQuery(sqlQuery);
    
    // 4. Return response
    return { status: 200, jsonBody: result };
  }
});
```

**Pagination Pattern:**
Most list endpoints support pagination with:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10-200)
- `sortBy` - Sort field
- `sortOrder` - asc/desc
- `filters` - Object with filter criteria

### Azure Synapse Integration

The dashboard module integrates with Azure Synapse Analytics for:
- Pipeline run monitoring
- Activity run tracking
- Pipeline scheduling management
- Performance metrics collection

Uses `@azure/synapse-artifacts` client for Synapse API operations.

### CI/CD Pipeline

**Development Pipeline (`azure-pipelines-dev.yml`):**
- Triggers on `dev` branch
- Deploys to `lou-web-app-dev` Function App

**Production Pipeline (`azure-pipelines.yml`):**
- Triggers on `master` branch
- Deploys to `lou-web-app-prod` Function App
- Includes approval environment for production deployment

Both pipelines:
1. Install dependencies
2. Run tests (when implemented)
3. Build and package
4. Deploy to Azure Function App

### Development Guidelines

**Code Standards:**
- ESLint configuration enforces code quality
- Use `const`/`let` over `var`
- Strict equality (`===`) required
- Double quotes for strings
- Semicolons required
- 2-space indentation

**Error Handling:**
- All endpoints wrapped in try-catch
- Consistent error response format
- Logging via `context.log()`

**Security:**
- JWT authentication on all endpoints
- Role-based access control
- Database credentials in Key Vault
- SQL injection prevention via parameterized queries

### Common Development Tasks

**Adding a New Endpoint:**
1. Create function in `src/functions/`
2. Import required helpers and config
3. Implement authentication check
4. Add business logic
5. Test locally with `func start`

**Modifying Database Queries:**
- Update queries in function files
- Use helper functions from `helper/helper.js`
- Test with local database connection

**Debugging:**
```bash
# Run with debugging
func start --verbose

# Check logs in Application Insights (production)
# Access via Azure Portal
```

## Project Status

Active development areas documented in `TASKS.md`:
- Pipeline monitoring dashboard enhancements
- Activity-level pipeline information
- Schedule management implementation
- Performance metrics and reporting

## Required Tools

- Node.js (v18+ recommended)
- Azure Functions Core Tools v4 (`npm install -g azure-functions-core-tools@4`)
- Azure CLI (for deployment)
- SQL Server tools (for database operations)
